import {useCognitoAuthStore} from '@/stores/cognitoAuth'
import {useLocale} from 'vuetify'
import {useBidConfirmStore} from '../stores/bidConfirm'
import {useMessageDialogStore} from '../stores/messag-dialog'
import {useSearchResultStore} from '../stores/search-results'
import useBid from './bid'
import {localeString2Number} from './common'

/**
 * 一括入札関連の処理
 */
export default function useBidBulk() {
  const {t: translate} = useLocale()
  const auth = useCognitoAuthStore()
  const dialog = useMessageDialogStore()
  const bidConfirmStore = useBidConfirmStore()
  const searchStore = useSearchResultStore()
  const {validateBidInput} = useBid()

  const showBidConfirmDialog = bidList => {
    bidConfirmStore.bidConfirmDialogResetParams()
    bidConfirmStore.data = bidList
    bidConfirmStore.toggleBidConfirmDialog()
  }

  // 「入札する」ボタンクリックした際に処理
  const bidBulkHandle = params => {
    auth.reloadCookies()
    if (!auth.isAuthenticated) {
      dialog.setShowMessage(translate('ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE'), {
        showOkButton: true,
        showCloseButton: false,
      })
      return
    }
    let validationError = false
    // Get all input values
    const bidList = searchStore.productList.exhibitionList
      .filter(
        x => typeof params?.exhibitionNo === 'undefined' || x.exhibition_no === params.exhibitionNo
      )
      .map(x => {
        const all = searchStore.productList.all
          .filter(y => y.exhibition_no === x.exhibition_no)
          .map(z => {
            if (!z.bidQuantity && !z.bidPrice) {
              return null
            }
            // Validate the bid price and quantity only if they are not empty
            const validate = validateBidInput({
              inputBidPrice: z.bidPrice,
              inputBidQuantity: z.bidQuantity,
              enteredBidPrice: z.bid_status.bid_price,
              enteredBidQuantity: z.bid_status.bid_quantity,
              lowestBidPrice: z.bid_status.lowest_bid_price,
              lowestBidQuantity: z.bid_status.lowest_bid_quantity,
              pitchWidth: z.bid_status.pitch_width || 0,
              maxQuantity: z.bid_status.quantity,
            })
            if (validate.bidPriceErr || validate.bidQuantityErr) {
              validationError = true
              z.bidInputError = {...validate}
              return null
            }
            // Check input changed
            if (
              localeString2Number(z.bidQuantity) === z.bid_status.bid_quantity &&
              localeString2Number(z.bidPrice) === z.bid_status.bid_price
            ) {
              return null
            }

            // success
            return {
              exhibitionItemNo: z.exhibition_item_no,
              freeField: z.free_field,
              bidQuantity: localeString2Number(z.bidQuantity),
              bidPrice: localeString2Number(z.bidPrice),
              bidTotalPrice: localeString2Number(z.bidQuantity) * localeString2Number(z.bidPrice),
            }
          })
          .filter(Boolean)

        // If there is no bid, return null
        if (all.length === 0) {
          return null
        }

        const bidTotalPrice = all.reduce((acc, cur) => acc + cur.bidTotalPrice, 0)
        return {
          exhibitionNo: x.exhibition_no,
          exhibitionName: x.exhibition_name,
          bidTotalPrice,
          bidList: all,
        }
      })
      .filter(Boolean)

    // Validate the bid price
    if (validationError) {
      dialog.setShowMessage(translate('bulkBid.inputErrorMessage'), {isErr: true})
      return
    }

    // Check if there is no bid
    if (bidList.length === 0) {
      dialog.setShowMessage(translate('bulkBid.noBid'), {isErr: true})
      return
    }

    // Show the bid confirm dialog
    showBidConfirmDialog(bidList)
  }

  return {
    showBidConfirmDialog,
    bidBulkHandle,
  }
}
