import axios, {type AxiosError, type AxiosResponse} from 'axios'
import pako from 'pako'
import {useRouter} from 'vue-router'
import {useCognitoAuthStore} from '../stores/cognitoAuth.ts'
import {useLanguageStore} from '../stores/language.js'
import {useReloadAttemptStore} from '../stores/reload-attempt.js'
import {useLoaderStore} from '../stores/ui.js'
import {getMockData} from './mock/mockApi.js'

// API related type definitions
export type ApiRequestParams = {
  [key: string]: any
  languageCode?: string
}

export type ApiHeaders = {
  [key: string]: string | undefined
  authorization?: string
  'Content-Type'?: string
}

export type UseApiReturn = {
  apiExecute: <T = any>(
    path: string,
    params?: ApiRequestParams,
    fileUpload?: boolean,
    headers?: ApiHeaders
  ) => Promise<T>
  parseHtmlResponseError: (error: AxiosError | any) => any
}

export type BufferResponse = {
  type: 'Buffer'
  data: number[]
}

const VITE_API_ENDPOINT = import.meta.env.VITE_API_ENDPOINT

const isMockMode = (): boolean => {
  // console.log('🔍 Mock mode check:', {
  //   isDev,
  //   token,
  //   isMockToken,
  //   isProductionAPI,
  //   mockMode,
  //   forceMockMode,
  // })
  return false
}

/**
 * @module composables/useApi
 * API composable with TypeScript support
 */
export default function useApi(): UseApiReturn {
  const router = useRouter()
  const loader = useLoaderStore()
  const attempt = useReloadAttemptStore()
  const languageStore = useLanguageStore()
  const cognitoAuth = useCognitoAuthStore()

  /**
   * Execute API request with proper typing
   * @param path - API endpoint path
   * @param params - Request parameters
   * @param fileUpload - Whether this is a file upload request
   * @param headers - Additional headers
   * @returns Promise with typed response
   */
  const apiExecute = <T = any>(
    path: string,
    params: ApiRequestParams = {},
    fileUpload: boolean = false,
    headers: ApiHeaders = {}
  ): Promise<T> => {
    loader.setLoading(true)

    if (isMockMode()) {
      console.log(`🎭 Mock API ${path}:`, params)
      const mockData = getMockData(path)
      loader.setLoading(false)
      return Promise.resolve(mockData as T)
    } else {
      console.log(`🌐 Real API ${path}:`, params)
    }

    return Promise.resolve()
      .then(() => {
        const axs = axios.create({
          baseURL: VITE_API_ENDPOINT,
          timeout: 30000,
          headers: {
            'Content-Type': fileUpload ? 'application/octet-stream' : 'application/json',
          },
        })

        const token = cognitoAuth.idToken
        const authHeader = token ? `Bearer ${token}` : 'public'

        const config = {
          headers: {
            authorization: authHeader,
            ...headers,
          },
        }

        return axs.post(
          path,
          Object.assign({languageCode: languageStore.language || 'ja'}, params),
          config
        )
      })
      .then((response: AxiosResponse) => {
        let data = response.data
        const contentType = response.headers['content-type'] || ''

        // Check if response.data.data exists and contains compressed data
        if (response.data.data !== undefined) {
          try {
            const bufferData = Array.isArray(response.data.data)
              ? new Uint8Array(response.data.data)
              : response.data.data
            data = pako.inflate(bufferData, {to: 'string'})
          } catch (e) {
            console.warn('Failed to decompress data, using raw response:', e)
            // If decompression fails, use the original response data
            data = response.data
          }
        }
        // If response.data.data doesn't exist, the data is already in response.data

        if (contentType.includes('application/json')) {
          try {
            // If data is already an object (Axios auto-parsed), use it directly
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data
            console.log(`⚙️API ${path}: `, parsedData)
            return parsedData as T
          } catch (e) {
            console.error('Error parsing JSON:', data, e)
            throw new Error('Invalid JSON response')
          }
        } else if (contentType.includes('text/html')) {
          console.warn(
            '⚠️ Possibly error cause have not deploy api gateway yet or overridden token cause multiple apps on localhost'
          )
          console.error('HTML response received ...', data.toString())
          return {
            error: 'HTML response received',
            rawResponse: data.toString(),
          } as T
        } else {
          console.warn(`⚠️ Unexpected content-type: ${contentType}`)
          // Try to parse as JSON anyway, fallback to raw data
          try {
            const parsedData = JSON.parse(data.toString())
            console.log(`⚙️API ${path} (fallback JSON): `, parsedData)
            return parsedData as T
          } catch (e) {
            console.warn('Failed to parse as JSON, returning raw data')
            return data as T
          }
        }
      })
      .catch((error: AxiosError) => {
        console.error('❌️ API Request Failed:', error.response?.data || error.message || error)
        loader.setLoading(false)
        return Promise.reject(error)
      })
      .finally(() => {
        loader.setLoading(false)
      })
  }

  /**
   * Parse HTML response errors with proper typing
   * @param error - Axios error or any error object
   * @returns Parsed error data
   */
  const parseHtmlResponseError = (error: AxiosError | any): any => {
    console.log('error', error)

    if (error.response && (error.response.status === 400 || error.response.status === 409)) {
      // SQL errors to display on screen
      if (error.response.data.errors) {
        return error.response.data.errors
      }
      return error.response.data
    }

    if (error.response && error.response.status === 401) {
      console.log('401 Unauthorized ')
      return error.response.data
    }

    if (error.response && error.response.status === 403) {
      console.log('403 Forbidden - logging out user')
      cognitoAuth.logout().catch(console.error)
      return error?.response?.data
    }

    if (error.code === 'ERR_NETWORK') {
      if (attempt.isMaxAttemptsReached()) {
        console.error('An error occurred:', error)
        attempt.reset()
      } else {
        attempt.increment()
        console.log(
          `Network error occurred. Reload attempt ${attempt.reloadAttempts} of ${attempt.maxReloadAttempts}.`
        )
      }
      return error.message
    }

    return error?.response?.data || error
  }

  return {
    apiExecute,
    parseHtmlResponseError,
  }
}

/**
 * Type guard to check if response data is a Buffer response
 * @param data - Response data to check
 * @returns True if data is BufferResponse
 */
function isBufferResponse(data: any): data is BufferResponse {
  return data && typeof data === 'object' && data.type === 'Buffer' && Array.isArray(data.data)
}
