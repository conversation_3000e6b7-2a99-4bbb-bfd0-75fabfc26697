import useSearchProducts from '@/composables/searchProducts'
import {useCognitoAuthStore} from '@/stores/cognitoAuth'
import {useRouter} from 'vue-router'
import {useLocale} from 'vuetify'
import {useBidConfirmStore} from '../stores/bidConfirm'
import {useMessageDialogStore} from '../stores/messag-dialog'
import {useSearchResultStore} from '../stores/search-results'
import useApi from './useApi'

/**
 * Favorite processing
 */
export default function useFavorite() {
  const bidConfirmStore = useBidConfirmStore()
  const auth = useCognitoAuthStore()
  const msgStore = useMessageDialogStore()
  const searchResultStore = useSearchResultStore()
  const {search} = useSearchProducts()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const {t: translate} = useLocale()
  const router = useRouter()

  const toggleFavorite = async (exhibition_item_no, favorited) => {
    const params = {
      exhibition_item_no,
      favorited,
    }
    const productInMyPage = searchResultStore?.productList?.all?.find(
      product => product.exhibition_item_no === exhibition_item_no
    )
    const productInDetailPage = searchResultStore?.productDetails
    try {
      if (auth.isAuthenticated) {
        // Update store immediately for optimistic UI
        const isRemoving = favorited // if favorited is true, we're removing from favorites

        // Update favorite state when user is in mypage
        if (productInMyPage) {
          productInMyPage.attention_info.is_favorited = !productInMyPage.attention_info.is_favorited
          // productInMyPage.attention_info.favorited_count += isRemoving ? -1 : 1
        }

        // Update favorite state when user is in product detail page
        if (productInDetailPage && productInDetailPage.exhibition_item_no === exhibition_item_no) {
          productInDetailPage.attention_info.is_favorited =
            !productInDetailPage.attention_info.is_favorited
          productInDetailPage.attention_info.favorited_count += isRemoving ? -1 : 1
        }

        // Call API
        await apiExecute('private/favorite-item', params)

        // Update via store method for additional consistency
        searchResultStore.updateFavoriteCount(exhibition_item_no, !favorited, router)
      } else {
        console.warn('❌️User is not authenticated, showing login required message')
        msgStore.setShowMessage(translate('FAVORITE_LOGIN_REQUIRED_FAVORITE'), {
          showOkButton: true,
        })
      }
    } catch (error) {
      // Revert optimistic updates on error
      if (auth.isAuthenticated) {
        const isRemoving = favorited

        if (productInMyPage) {
          productInMyPage.attention_info.is_favorited = !productInMyPage.attention_info.is_favorited
          productInMyPage.attention_info.favorited_count += isRemoving ? 1 : -1
        }

        if (productInDetailPage && productInDetailPage.exhibition_item_no === exhibition_item_no) {
          productInDetailPage.attention_info.is_favorited =
            !productInDetailPage.attention_info.is_favorited
          productInDetailPage.attention_info.favorited_count += isRemoving ? 1 : -1
        }
      }
      parseHtmlResponseError(error)
    }
  }

  return {toggleFavorite}
}
