@charset "UTF-8";
/* *  Remodal - v1.1.1
 * *  Responsive, lightweight, fast, synchronized with CSS animations, fully customizable modal window plugin with declarative configuration and hash tracking.
 * *  http://vodkabears.github.io/remodal/
 * *
 * *  Made by <PERSON><PERSON>
 * *  Under MIT License */
/* ==========================================================================
 * * Remodal's default mobile first theme
 * * ========================================================================== */
/* Default theme styles for the background */
.remodal-bg.remodal-is-opening,
.remodal-bg.remodal-is-opened {
  -webkit-filter: blur(3px);
  filter: blur(3px);
}

/* Default theme styles of the overlay */
.remodal-overlay {
  /*background: rgba(43, 46, 56, 0.9); */
}
.remodal-overlay.remodal-is-opening,
.remodal-overlay.remodal-is-closing {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.remodal-overlay.remodal-is-opening {
  -webkit-animation-name: remodal-overlay-opening-keyframes;
  animation-name: remodal-overlay-opening-keyframes;
}
.remodal-overlay.remodal-is-closing {
  -webkit-animation-name: remodal-overlay-closing-keyframes;
  animation-name: remodal-overlay-closing-keyframes;
}

/* Default theme styles of the wrapper */
.remodal-wrapper {
  padding: 10px 10px 0;
}

/* Default theme styles of the modal dialog */
.remodal {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 10px;
  padding: 80px 35px;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  color: #2b2e38;
  /*background: #fff; */
  background-color: rgba(247, 247, 247, 0.95);
  -webkit-box-shadow: 0 5px 27px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 5px 27px 0 rgba(0, 0, 0, 0.15);
  text-align: center;
  vertical-align: middle;
}
.remodal.remodal-is-opening,
.remodal.remodal-is-closing {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.remodal.remodal-is-opening {
  -webkit-animation-name: remodal-opening-keyframes;
  animation-name: remodal-opening-keyframes;
}
.remodal.remodal-is-closing {
  -webkit-animation-name: remodal-closing-keyframes;
  animation-name: remodal-closing-keyframes;
}

/* Vertical align of the modal dialog */
.remodal-wrapper:after {
  vertical-align: middle;
}

/* Close button */
.remodal-close {
  position: absolute;
  /*top: 0; */
  top: -18px;
  /*left: 0; */
  right: -18px;
  display: block;
  overflow: visible;
  /*width: 35px; */
  width: 60px;
  height: 60px;
  /*height: 35px; */
  margin: 0;
  padding: 0;
  cursor: pointer;
  -webkit-transition: color 0.2s;
  transition: color 0.2s;
  text-decoration: none;
  /*color: #95979c; */
  color: #fff;
  border: 0;
  outline: 0;
  /*background: transparent; */
  background-color: #427fae;
  border-radius: 50%;
}
.remodal-close:hover,
.remodal-close:focus {
  /*color: #2b2e38; */
  color: #fff;
}
.remodal-close:before {
  /*font-family: Arial, "Helvetica CY", "Nimbus Sans L", sans-serif !important; */
  font-size: 25px;
  /*line-height: 35px; */
  line-height: 58px;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  /*width: 35px; */
  width: 60px;
  /*content: "\00d7"; */
  /* content: "×"; */
  text-align: center;
}

@media only screen and (max-width: 767px) {
  .remodal-close {
    width: 40px;
    height: 40px;
    top: -5px;
    right: -5px;
  }
  .remodal-close:before {
    font-size: 20px;
    width: 40px;
    line-height: 40px;
  }
}
/* Dialog buttons */
.remodal-confirm,
.remodal-cancel {
  font: inherit;
  display: inline-block;
  overflow: visible;
  min-width: 110px;
  margin: 0;
  padding: 12px 0;
  cursor: pointer;
  -webkit-transition: background 0.2s;
  transition: background 0.2s;
  text-align: center;
  vertical-align: middle;
  text-decoration: none;
  border: 0;
  outline: 0;
}

.remodal-confirm {
  color: #fff;
  background: #81c784;
}
.remodal-confirm:hover,
.remodal-confirm:focus {
  background: #66bb6a;
}

.remodal-cancel {
  color: #fff;
  background: #e57373;
}
.remodal-cancel:hover,
.remodal-cancel:focus {
  background: #ef5350;
}

/* Remove inner padding and border in Firefox 4+ for the button tag. */
.remodal-confirm::-moz-focus-inner,
.remodal-cancel::-moz-focus-inner,
.remodal-close::-moz-focus-inner {
  padding: 0;
  border: 0;
}

/* Keyframes
 * * ========================================================================== */
@-webkit-keyframes remodal-opening-keyframes {
  from {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
    -webkit-filter: blur(0);
    filter: blur(0);
  }
}
@keyframes remodal-opening-keyframes {
  from {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
    -webkit-filter: blur(0);
    filter: blur(0);
  }
}
@-webkit-keyframes remodal-closing-keyframes {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  to {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    -webkit-filter: blur(0);
    filter: blur(0);
  }
}
@keyframes remodal-closing-keyframes {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  to {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    -webkit-filter: blur(0);
    filter: blur(0);
  }
}
@-webkit-keyframes remodal-overlay-opening-keyframes {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes remodal-overlay-opening-keyframes {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes remodal-overlay-closing-keyframes {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes remodal-overlay-closing-keyframes {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
/* Media queries
 * * ========================================================================== */
@media only screen and (min-width: 641px) {
  .remodal {
    /*max-width: 700px; */
    max-width: 540px;
  }
}
/* IE8
 * * ========================================================================== */
.lt-ie9 .remodal-overlay {
  background: #2b2e38;
}
.lt-ie9 .remodal {
  width: 700px;
}
/*# sourceMappingURL=remodal-default-theme.css.map */
