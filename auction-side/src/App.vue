<script setup>
  import {useCommonConstantsStore} from '@/stores/common-constants'
  import {computed, onBeforeMount, onMounted, watch} from 'vue'

  import FooterComponent from '@/components/common/FooterComponent.vue'
  import HeaderComponent from '@/components/common/HeaderComponent.vue'
  import LoaderModal from '@/components/parts/LoaderModal.vue'
  // import ShowMessageDialog from '@/components/parts/ShowMessageDialog.vue'
  import {scrollToAnker} from '@/composables/common'
  import {useLanguageStore} from '@/stores/language'
  import {useTenantSettingsStore} from '@/stores/tenantSettings'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'

  const {t, current: locale} = useLocale()
  // const auth = useAuthStore()
  const {fetchTenantSettings} = useTenantSettingsStore()
  const route = useRoute()
  const commonConstants = useCommonConstantsStore()
  const languageStore = useLanguageStore()

  const isSp = window.matchMedia('(max-width: 767px)')

  const bodyId = computed(() => route.meta.bodyId || '')
  const bodyClass = computed(() => route.meta.bodyClass || '')
  const mainClass = computed(() => route.meta.mainClass || '')

  const onLoadResize = () => {
    if (isSp.matches) {
      $("meta[name='viewport']").attr('content', 'width=device-width,initial-scale=1')
    } else {
      $("meta[name='viewport']").attr('content', 'width=1200')
    }
  }

  // Initialize language system
  const initializeLanguage = () => {
    const currentLang = languageStore.currentLanguage
    locale.value = currentLang
  }

  onBeforeMount(() => {
    // auth.reloadCookies()
    // Initialize language system
    initializeLanguage()
  })

  onMounted(async () => {
    window.addEventListener('resize', onLoadResize)
    const urlHash = location.hash
    if (urlHash) {
      $('body,html').stop().scrollTop(0)
      setTimeout(() => {
        scrollToAnker(urlHash)
      }, 100)
    }

    // Load common constants
    commonConstants.getConstants()

    try {
      await fetchTenantSettings()
    } catch (error) {
      console.warn('Failed to load tenant settings:', error)
    }
  })

  watch(
    () => languageStore.currentLanguage,
    () => {
      window.document.title = t('SITE_TITLE')
    }
  )
</script>

<template>
  <div :id="bodyId" :class="bodyClass">
    <HeaderComponent v-if="route.meta.headerShow !== false" />
    <v-app id="main" :class="mainClass">
      <v-main class="stock">
        <RouterView />
      </v-main>
    </v-app>
    <FooterComponent v-if="route.meta.headerShow !== false" />
    <LoaderModal />
    <!-- <ShowMessageDialog /> -->
  </div>
</template>
