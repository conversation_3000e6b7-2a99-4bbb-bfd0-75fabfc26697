<script setup>
  import {useCognitoAuthStore} from '@/stores/cognitoAuth.js'
  import {computed, onMounted, ref, watch} from 'vue'
  import {useRoute, useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useSearchProducts from '../../composables/searchProducts.js'
  import {PATH_NAME} from '../../defined/const.js'
  import {useCommonConstantsStore} from '../../stores/common-constants.js'
  import useChangeLanguage, {useLanguageStore} from '../../stores/language.js'
  import {useSearchResultStore} from '../../stores/search-results.js'

  const {t: translate, current} = useLocale()
  const route = useRoute()
  const router = useRouter()
  const auth = useCognitoAuthStore()
  const {search} = useSearchProducts()
  const searchStore = useSearchResultStore()
  const commonConstants = useCommonConstantsStore()
  const localSearchKey = ref('')
  const languageStore = useLanguageStore()
  const {changeLanguage} = useChangeLanguage()

  watch(
    () => searchStore.searchKey,
    isNewVal => {
      localSearchKey.value = isNewVal
    }
  )

  // Handle "Search" on Header (PC and SP)
  const handleHeaderSearch = async () => {
    if (route.path !== PATH_NAME.TOP) {
      await router.push(PATH_NAME.TOP)
    }

    searchStore.setSearchKeyTop(localSearchKey.value)
    searchStore.categoryList.length = 0
    await search({
      searchKey: searchStore.searchKeyTop,
      categoryList: [],
      modelList: [],
      unSoldOut: false,
    })

    // SP: SlideUP the gNav after category selected
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
  }

  // SPでカテゴリをクリックしたときの検索を処理する
  const handleCategorySearch = async cat => {
    searchStore.setSearchKeyTop(localSearchKey.value)
    searchStore.categoryList.length = 0
    searchStore.categoryList.push(cat)
    await search({
      searchKey: searchStore.searchKeyTop,
      categoryList: [].concat(cat),
      modelList: [],
      unSoldOut: false,
    })
  }

  const selectCategory = async cat => {
    console.log('selected category: ', cat)
    // Go to top page incase not in top page
    if (route.path !== PATH_NAME.TOP) {
      await router.push(PATH_NAME.TOP)
    }

    // SlideUP the gNav after category selected
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
    handleCategorySearch(cat)
  }

  const categoryList = computed(() =>
    (commonConstants.constants || []).filter(x => x.key_string === 'PRODUCT_CATEGORY')
  )

  const handleChangeLanguage = e => {
    const lang = e.target.value
    changeLanguage(lang)
    languageStore.setLanguage(lang)
  }

  watch(
    () => languageStore.language,
    () => {
      current.value = languageStore.language
      commonConstants.getConstants()
    }
  )
  onMounted(() => {
    // Toggle navigation menu on mobile
    $('header p.btnMenu').on('click', function () {
      $('header .gNav').slideToggle()
      $(this).toggleClass('close')
    })

    // SP Nav child open/close in Header
    $('header .gNav nav ul.only_sp li p').on('click', function () {
      $(this).next('ul').slideToggle()
      $(this).toggleClass('close')
    })
    // Hide the gNav after clicking the most child element
    $('header .gNav nav ul.only_sp li ul li').on('click', () => {
      $('header .gNav').slideUp()
      $('.btnMenu').removeClass('close')
    })

    // SP Nav child open/close in Footer
    $('footer nav .fNav_sp > ul > li > p').on('click', function () {
      $(this).next('ul,dl').slideToggle()
      $(this).toggleClass('close')
    })
  })

  watch(
    () => route.path,
    () => {
      // SlideUP the gNav after path changed
      $('header .gNav').slideUp()
      $('.btnMenu').removeClass('close')
    }
  )
</script>

<template>
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="h-top">
        <div class="h-top-logo">
          <router-link class="logo" :to="PATH_NAME.TOP">
            <img src="@/assets/img/common/logo_worldmobile.svg" alt="WORLDMOBILE" />
          </router-link>
        </div>
        <!--Start-->
        <div class="nav-elm">
          <div class="nav-category only_pc">
            <li>
              <a href="#">{{ translate('top.appBar.selectCategory') }}</a>
              <div class="category-list">
                <ul>
                  <li
                    v-for="cat in categoryList"
                    :key="cat"
                    class="cursor-pointer"
                    @click="selectCategory(cat.value1)"
                  >
                    <a>{{ cat.value2 }}</a>
                  </li>
                </ul>
              </div>
            </li>
          </div>
          <div class="search-elm only_pc">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="side-search-keyword search-keyword"
              :placeholder="translate('filterBox.inputPlaceholder')"
              v-model="localSearchKey"
            />
            <button @click="handleHeaderSearch()" class="d-flex align-center justify-center">
              <img src="@/assets/img/common/icn_search.svg" />
            </button>
          </div>

          <div class="lang-wrap">
            <select id="locale-switcher" class="lang" @change="handleChangeLanguage">
              <option value="ja" :selected="current === 'ja'">JA</option>
              <option value="en" :selected="current === 'en'">EN</option>
            </select>
          </div>

          <ul class="nav-btn only_pc">
            <li v-if="!auth.isAuthenticated" class="nav-account registration">
              <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                <span>{{ translate('top.appBar.register') }}</span>
              </RouterLink>
            </li>
            <li v-if="!auth.isAuthenticated" class="nav-account login">
              <RouterLink :to="PATH_NAME.LOGIN">
                <span>{{ translate('top.appBar.login') }}</span>
              </RouterLink>
            </li>
            <li v-else class="nav-account logout">
              <a @click="auth.showLogoutMessage" class="cursor-pointer"
                ><span>{{ translate('top.appBar.logout') }}</span></a
              >
            </li>
          </ul>
        </div>
        <!--End-->
        <p class="btnMenu only_sp"><span class="ham"></span></p>
      </div>
    </div>
    <div class="wrap-header-nav-elm only_pc">
      <div class="gnavi__wrap">
        <ul class="gnavi__lists">
          <li class="gnavi__list">
            <a href="#" class="arrow">{{ translate('guidance.auction') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.TOP">
                  {{ translate('guidance.auctionOngoing') }}
                </RouterLink>
              </li>
              <li v-if="auth.isAuthenticated" class="dropdown__list">
                <RouterLink :to="PATH_NAME.BID_HISTORY_ALL">
                  {{ translate('guidance.auctionResult') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li v-if="auth.isAuthenticated" class="gnavi__list">
            <a href="#" class="arrow">{{ translate('user.myPage') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.FAVORITES">
                  {{ translate('HEADER_NAV_FAVORITES') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.BIDS">
                  {{ translate('HEADER_NAV_BIDDING') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.BID_HISTORY">
                  {{ translate('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.MYPAGE_EDIT">
                  {{ translate('MYPAGE_EDIT_PROFILE') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li v-if="!auth.isAuthenticated" class="gnavi__list">
            <a href="#" class="arrow">{{ translate('guidance.aboutMembership') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                  {{ translate('guidance.register') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.LOGIN">
                  {{ translate('guidance.login') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="gnavi__list">
            <a href="#" class="arrow">{{ translate('guidance.firstTime') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.GUIDE">
                  {{ translate('guidance.guide') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="gnavi__list">
            <a href="#" class="arrow">{{ translate('guidance.information') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.CONTACT">
                  {{ translate('guidance.contact') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="gnavi__list">
            <a href="#" class="arrow">{{ translate('guidance.aboutUs') }}</a>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.PROFILE">
                  {{ translate('guidance.companyOverview') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.TERMS">
                  {{ translate('guidance.terms') }}
                </RouterLink>
              </li>
              <li class="dropdown__list break-line">
                <RouterLink :to="PATH_NAME.ORDER_CONTRACT">
                  {{ translate('guidance.toshuho') }}
                </RouterLink>
              </li>
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.PRIVACY">
                  {{ translate('guidance.privacy') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <!-- 以下省略 -->
        </ul>
      </div>
    </div>
    <!-- gNav PC/SP end -->
    <!-- gNav SP start -->
    <div class="gNav only_sp">
      <nav>
        <ul class="only_sp">
          <li class="search">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="search-keyword search-keyword-scope"
              v-model="localSearchKey"
              :placeholder="translate('filterBox.inputPlaceholder')"
            />
            <button @click="handleHeaderSearch()">
              <img src="@/assets/img/common/icn_search_dgray.svg" />
            </button>
          </li>
          <li class="nav-black">
            <p>{{ translate('filterBox.category') }}</p>
            <ul>
              <li
                v-for="cat in categoryList"
                :key="cat"
                class="#"
                @click="selectCategory(cat.value1)"
              >
                <a>{{ cat.value2 }}</a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.auction') }}</p>
            <ul class="dropdown__lists">
              <li class="dropdown__list">
                <RouterLink :to="PATH_NAME.TOP">
                  {{ translate('guidance.auctionOngoing') }}
                </RouterLink>
              </li>
              <li v-if="auth.isAuthenticated" class="dropdown__list">
                <RouterLink :to="PATH_NAME.BID_HISTORY_ALL">
                  {{ translate('guidance.auctionResult') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li v-show="auth.isAuthenticated" class="nav-black">
            <p>{{ translate('user.myPage') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.FAVORITES">
                  {{ translate('HEADER_NAV_FAVORITES') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.BIDS">
                  {{ translate('HEADER_NAV_BIDDING') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.BID_HISTORY">
                  {{ translate('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.MYPAGE_EDIT">
                  {{ translate('MYPAGE_EDIT_PROFILE') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li v-show="!auth.isAuthenticated" class="nav-black">
            <p>{{ translate('guidance.aboutMembership') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                  {{ translate('top.appBar.register') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.LOGIN">
                  {{ translate('top.appBar.login') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.firstTime') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.GUIDE">
                  {{ translate('guidance.guide') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.information') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.CONTACT">
                  {{ translate('guidance.contact') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.aboutUs') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.PROFILE">
                  {{ translate('guidance.companyOverview') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.TERMS">
                  {{ translate('guidance.terms') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.ORDER_CONTRACT">
                  {{ translate('guidance.toshuho') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.PRIVACY">
                  {{ translate('guidance.privacy') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li v-if="auth.isAuthenticated">
            <p class="ttl">
              <a @click="auth.showLogoutMessage">{{ translate('top.appBar.logout') }}</a>
            </p>
          </li>
        </ul>
      </nav>
    </div>
    <!-- gNav SP end -->
  </header>
</template>
<!-- <style lang="css" scoped>
  header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a.arrow:before {
    box-sizing: content-box;
  }
  header .wrap-header-elm .nav-elm .lang-wrap:after {
    box-sizing: content-box;
  }
  .break-line {
    word-break: break-word;
  }
  .search-keyword-scope {
    height: 44px;
    width: calc(100% - 45px);
    padding: 11px 18px 13px;
    border-right: none;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  @media screen and (max-width: 767px) and (max-width: 767px) {
    .search-keyword-scope {
      border: none;
    }
  }
  @media screen and (max-width: 767px) {
    .search-keyword-scope {
      width: 43px;
      height: 44px;
      background-color: #fff;
      border: 1px solid #e4e4e4;
      border-left: none;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
  @media screen and (max-width: 767px) and (max-width: 767px) {
    .search-keyword-scope {
      border: none !important;
    }
  }
  header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists {
    width: 240px;
  }
</style> -->
