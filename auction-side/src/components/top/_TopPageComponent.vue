<script setup>
  import {useLanguageRefetch} from '@/composables/useLanguageRefetch'
  import {useLanguageStore} from '@/stores/language'
  import {defineAsyncComponent, onBeforeMount, onMounted} from 'vue'
  import {useLocale} from 'vuetify'
  import useSearchProducts from '../../composables/searchProducts'
  import useApi from '../../composables/useApi'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import TopAuctionList from './TopAuctionList.vue'

  const NoticeList = defineAsyncComponent(() => import('./NoticeList.vue'))
  const NewsHeadline = defineAsyncComponent(() => import('./NewsHeadline.vue'))
  const FilterBox = defineAsyncComponent(() => import('../search-list/parts/FilterBox.vue'))

  const {t, current} = useLocale()
  const languageStore = useLanguageStore()
  const {parseHtmlResponseError} = useApi()
  const dialog = useMessageDialogStore()
  const {getConstants, search, resetSearchFilters} = useSearchProducts()
  const {refetchOnLanguageChange} = useLanguageRefetch()

  const getData = async () => {
    try {
      await Promise.all([getConstants(), search({initLimit: 20})])
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
      dialog.setShowMessage(err.message ?? t('COMMON_ERROR'), {isErr: true})
    }
  }

  onBeforeMount(async () => {
    resetSearchFilters()
    await getData()
  })

  // Set up language change watcher
  onMounted(() => {
    refetchOnLanguageChange(getData)
  })
</script>

<template>
  <Suspense>
    <!-- <NewsHeadline /> -->
    <!-- <FilterBox /> -->
    <!-- <TopPageExhibitions /> -->
    <TopAuctionList />
    <!-- <NoticeList /> -->
  </Suspense>
</template>
