<script setup lang="ts">
  import type {ProductListHandlers} from '@/components/common/auction/types'
  import {useViewMode} from '@/components/common/auction/useViewMode'
  import BreadCrumb from '@/components/common/BreadCrumb.vue'
  import type {AuctionClassification, FormattedAuctionItem} from '@/composables/_type.ts'
  import useSearchProducts from '@/composables/searchProducts'
  import useApi from '@/composables/useApi'
  import {CLASSIFICATIONS} from '@/defined/const'
  import {useSearchResultStore} from '@/stores/search-results.js'
  import {useTenantSettingsStore} from '@/stores/tenantSettings'
  import {computed, defineAsyncComponent, onBeforeMount, ref} from 'vue'
  import SearchResultFilterBox from './SearchResultFilterBox.vue'

  const PanelAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/PanelAuctionItem.vue')
  )
  const RowBidAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/RowBidAuctionItem.vue')
  )

  const {viewMode, toggleView} = useViewMode()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const store = useSearchResultStore()
  const tenantSettingsStore = useTenantSettingsStore()
  const {productList, totalCount} = store
  const {getConstants} = useSearchProducts()

  const loading = ref(false)

  // Get search result view mode from tenant settings
  const {searchResultViewMode} = tenantSettingsStore

  const items = computed(() => {
    return productList.all || []
  })

  const filteredItems = computed(() => {
    const classificationNumber =
      store.selectedAucClassification === CLASSIFICATIONS.ASCENDING ? 1 : 2
    return items.value.filter(item => item.auction_classification === classificationNumber)
  })

  const viewOnlyItems = computed(() => {
    const filtered = items.value.filter(item => item.free_field.product_name)
    return filtered
  })
  const biddableItems = computed(() => {
    return items.value
  })
  const currentPage = ref(1)
  const itemsPerPage = 20 // This should match the API limit parameter

  // Update totalCount to reflect filtered items
  // TODO: This should be based on filtered items, use in future
  const filteredTotalCount = computed(() => {
    return filteredItems.value.length
  })

  const totalPages = computed(() => {
    return Math.ceil(totalCount / itemsPerPage)
  })

  const pageNumbers = computed(() => {
    const total = totalPages.value
    const current = currentPage.value
    const pages: number[] = []

    if (total <= 7) {
      // Show all pages if total is 7 or less
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // Show pages around current page
      if (current <= 4) {
        // Show first 5 pages + ... + last page
        for (let i = 1; i <= 5; i++) {
          pages.push(i)
        }
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
          pages.push(total)
        }
      } else if (current >= total - 3) {
        // Show first page + ... + last 5 pages
        pages.push(1)
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
        }
        for (let i = total - 4; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // Show first + ... + current-1, current, current+1 + ... + last
        pages.push(1)
        pages.push(-1) // Ellipsis indicator
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i)
        }
        pages.push(-1) // Ellipsis indicator
        pages.push(total)
      }
    }

    return pages
  })

  const canGoPrevious = computed(() => currentPage.value > 1)
  const canGoNext = computed(() => currentPage.value < totalPages.value)

  const currentItemsStart = computed(() => {
    return (currentPage.value - 1) * itemsPerPage + 1
  })

  const currentItemsEnd = computed(() => {
    return Math.min(currentPage.value * itemsPerPage, totalCount)
    // return Math.min(currentPage.value * itemsPerPage, filteredTotalCount.value)
  })

  // Pagination navigation functions
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
      currentPage.value = page
      console.log(`Navigating to page ${page}`)
    }
  }

  const goToPreviousPage = () => {
    if (canGoPrevious.value) {
      goToPage(currentPage.value - 1)
    }
  }

  const goToNextPage = () => {
    if (canGoNext.value) {
      goToPage(currentPage.value + 1)
    }
  }

  const handlers: ProductListHandlers = {
    onFavoriteToggle: async (exhibitionItemNo: string, currentFavorited: boolean) => {
      console.log('Toggle favorite:', exhibitionItemNo, currentFavorited)
      const item = items.value.find(item => item.exhibition_item_no === exhibitionItemNo)
      if (item) {
        item.attention_info.is_favorited = !currentFavorited
        if (!currentFavorited) {
          item.attention_info.favorited_count = (item.attention_info.favorited_count || 0) + 1
        } else {
          item.attention_info.favorited_count = Math.max(
            0,
            (item.attention_info.favorited_count || 0) - 1
          )
        }
      }
    },
    onBid: async (item: FormattedAuctionItem, bidPrice: string, bidQuantity: string) => {
      console.log('Place bid:', item.exhibition_item_no, bidPrice, bidQuantity)
      const targetItem = items.value.find(i => i.exhibition_item_no === item.exhibition_item_no)
      if (targetItem) {
        targetItem.attention_info.bid_count += 1
        const numericPrice =
          parseInt(bidPrice.replace(/,/g, '')) || targetItem.bid_status.current_price
        targetItem.bid_status.current_price = numericPrice
        targetItem.currentPrice = numericPrice.toLocaleString()
      }
    },
    onRefresh: async () => {
      await fetchProducts()
    },
    onItemClick: (item: FormattedAuctionItem) => {
      console.log('Item clicked:', item.exhibition_item_no)
    },
  }

  const fetchProducts = async () => {
    loading.value = true
    try {
      const params = {
        unSoldOut: false,
        favorite: false,
        bidding: false,
        languageCode: 'ja',
        initLimit: 20,
        limit: 20,
        auctionClassification: store.selectedAucClassification,
      }

      const response = await apiExecute('public/search-auction-items', params)
      store.setProductList(response)
    } catch (error) {
      console.error('Error fetching products:', error)
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  onBeforeMount(async () => {
    try {
      store.resetSearchFilters()
      store.resetProductList()
      await Promise.all([
        getConstants(),
        fetchProducts(),
        tenantSettingsStore.fetchTenantSettings(),
      ])
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
    }
  })

  // Add function to handle auction type switching
  const handleAuctionTypeChange = async (classificationType: AuctionClassification) => {
    const classificationValue =
      classificationType === 'ascending' ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
    store.selectedAucClassification = classificationValue
    currentPage.value = 1 // Reset to first page
    await fetchProducts()
  }

  const refreshList = async classification => {
    console.log(
      '%c 🖕: classification ',
      'font-size:16px;background-color:#9326b6;color:white;',
      classification
    )
    await fetchProducts()
  }
</script>

<template>
  <main id="main">
    <BreadCrumb customTitle="商品一覧" />

    <div class="auction-type-label">
      <div class="type-tab-wrap">
        <span v-if="store.selectedAucClassification === CLASSIFICATIONS.ASCENDING" class="active"
          >競り上がり式オークション</span
        >
        <a v-else @click="handleAuctionTypeChange('ascending')" href="#"
          >競り上がり式オークション</a
        >
        <span v-if="store.selectedAucClassification === CLASSIFICATIONS.SEALED" class="active"
          >封印入札式オークション</span
        >
        <a v-else @click="handleAuctionTypeChange('sealed')" href="#">封印入札式オークション</a>
      </div>
    </div>

    <h2 class="page-ttl list">
      <p class="sub">検索結果</p>
      <div class="ttl">商品一覧</div>
    </h2>

    <SearchResultFilterBox />
    <section id="list" class="list-item">
      <div class="container">
        <!-- リストタイプ A: Panel mode with no bid column and display toggle -->
        <template v-if="searchResultViewMode === 'panel'">
          <p class="list-type-label">
            <span>リストタイプ A</span
            >入札欄無し・表示切り替えあり（テナント設定によりタイプAかBが表示）
          </p>
          <!-- display-option -->
          <div class="display-option">
            <div class="refine">
              <div class="sorting">
                <button class="menu_trigger">
                  <span class="option_selected">おすすめ順</span>
                </button>
                <ul class="sorting_panel">
                  <li class="option_item"><a>おすすめ順</a></li>
                  <li class="option_item"><a>新着順</a></li>
                  <li class="option_item"><a>残り時間の少ない順</a></li>
                  <li class="option_item"><a>残り時間の長い順</a></li>
                  <li class="option_item"><a>現在価格の安い順</a></li>
                  <li class="option_item"><a>現在価格の高い順</a></li>
                  <li class="option_item"><a>入札件数の多い順</a></li>
                  <li class="option_item"><a>入札件数の少ない順</a></li>
                </ul>
              </div>
              <div class="check-onsale">
                <div class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" /><label
                    for="checkbox"
                    >販売中のみ</label
                  >
                </div>
              </div>
            </div>
            <div class="switch">
              <p class="dl">
                <a href="./"><span>商品譲歩ダウンロード（CSV）</span></a>
              </p>
              <div class="number-switch">
                <p class="label">表示件数</p>
                <div class="num">
                  <button class="btn is-active">20件</button>
                  <button class="btn">50件</button>
                  <button class="btn">100件</button>
                </div>
              </div>
              <div class="display-switch">
                <p>
                  <button
                    :class="['btn', 'panel', {'is-active': viewMode === 'panel'}]"
                    @click="toggleView('panel')"
                  ></button>
                </p>
                <p>
                  <button
                    :class="['btn', 'row', {'is-active': viewMode === 'row'}]"
                    @click="toggleView('row')"
                  ></button>
                </p>
              </div>
            </div>
          </div>

          <!-- 入札不可（入札欄無し）のオークション -->
          <div v-if="viewOnlyItems.length > 0" :class="['item-list', viewMode]">
            <ul>
              <PanelAuctionItem
                v-for="item in viewOnlyItems"
                :key="item.exhibition_item_no"
                :item="item"
                :view-mode="viewMode"
                :handlers="handlers"
                custom-classes=""
              />
            </ul>
          </div>
        </template>

        <!-- リストタイプ B: Row mode with bid column -->
        <template v-if="searchResultViewMode === 'row'">
          <p class="list-type-label">
            <span>リストタイプ B</span>入札欄あり（テナント設定によりタイプAかBが表示）
          </p>

          <div class="display-option">
            <div class="refine">
              <div class="sorting">
                <button class="menu_trigger">
                  <span class="option_selected">おすすめ順</span>
                </button>
                <ul class="sorting_panel">
                  <li class="option_item"><a>おすすめ順</a></li>
                  <li class="option_item"><a>新着順</a></li>
                  <li class="option_item"><a>残り時間の少ない順</a></li>
                  <li class="option_item"><a>残り時間の長い順</a></li>
                  <li class="option_item"><a>現在価格の安い順</a></li>
                  <li class="option_item"><a>現在価格の高い順</a></li>
                  <li class="option_item"><a>入札件数の多い順</a></li>
                  <li class="option_item"><a>入札件数の少ない順</a></li>
                </ul>
              </div>
              <div class="check-onsale">
                <div class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" /><label
                    for="checkbox"
                    >販売中のみ</label
                  >
                </div>
              </div>
            </div>
            <div class="switch">
              <p class="dl">
                <a href="./"><span>商品情報ダウンロード（CSV）</span></a>
              </p>
              <div class="number-switch">
                <p class="label">表示件数</p>
                <div class="num">
                  <button class="btn is-active">20件</button>
                  <button class="btn">50件</button>
                  <button class="btn">100件</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 商品一覧（入札欄あり　Biddable Items） -->
          <RowBidAuctionItem
            v-for="item in biddableItems"
            :key="item.exhibition_item_no"
            :item="item"
            @refresh="refreshList"
          />
        </template>

        <!-- Pagination -->
        <div v-if="totalPages >= 1" class="wrap-btn pagination">
          <!-- <p>{{ filteredTotalCount }}件中 {{ currentItemsStart }}〜{{ currentItemsEnd }}件を表示</p> -->
          <p>{{ totalCount }}件中 {{ currentItemsStart }}〜{{ currentItemsEnd }}件を表示</p>
          <nav class="pagination">
            <ul>
              <!-- Previous button -->
              <li class="prev">
                <a
                  href="#"
                  :class="{disabled: !canGoPrevious}"
                  @click.prevent="goToPreviousPage"
                ></a>
              </li>

              <!-- Page numbers -->
              <li v-for="page in pageNumbers" :key="page">
                <template v-if="page === -1">
                  <!-- Ellipsis -->
                  <span class="ellipsis">...</span>
                </template>
                <template v-else>
                  <a
                    href="#"
                    :class="{active: page === currentPage}"
                    @click.prevent="goToPage(page)"
                  >
                    {{ page }}
                  </a>
                </template>
              </li>

              <!-- Next button -->
              <li class="next">
                <a href="#" :class="{disabled: !canGoNext}" @click.prevent="goToNextPage"></a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
  /* Dynamic pagination styling */
  .pagination .ellipsis {
    padding: 8px 12px;
    color: #666;
    cursor: default;
  }

  .pagination a.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .pagination a.active {
    background-color: #007bff;
    color: white;
    border-radius: 4px;
  }

  .pagination a:hover:not(.disabled):not(.active) {
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  /* Auction type tab styling */
  .auction-type-label .type-tab-wrap span.active {
    font-weight: bold;
    color: #007bff;
    border-bottom: 2px solid #007bff;
  }

  .auction-type-label .type-tab-wrap a {
    cursor: pointer;
    color: #666;
    text-decoration: none;
  }

  .auction-type-label .type-tab-wrap a:hover {
    color: #007bff;
  }
</style>
