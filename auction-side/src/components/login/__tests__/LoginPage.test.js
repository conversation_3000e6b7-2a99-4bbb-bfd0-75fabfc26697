import {mount} from '@vue/test-utils'
import {createPinia, setActivePinia} from 'pinia'
import {beforeEach, describe, expect, it, vi} from 'vitest'
import {nextTick} from 'vue'
import {useCognitoAuthStore} from '../../../stores/cognitoAuth.ts'
import LoginPage from '../LoginPage.vue'

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
}

const mockRoute = {
  query: {},
  redirectedFrom: null,
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRoute,
}))

// Mock the cognitoAuth store
vi.mock('../../../stores/cognitoAuth.js', () => ({
  useCognitoAuthStore: vi.fn(),
}))

// Mock PATH_NAME constants
vi.mock('../../../defined/const.js', () => ({
  PATH_NAME: {
    TOP: '/top',
    REGISTER: '/register',
    COGNITO_REGISTER: '/cognito-register',
    REMINDER: '/reminder',
  },
}))

describe('LoginPage', () => {
  let pinia
  let mockCognitoAuth

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)

    mockCognitoAuth = {
      login: vi.fn(),
    }

    useCognitoAuthStore.mockReturnValue(mockCognitoAuth)

    vi.clearAllMocks()
  })

  const createWrapper = (options = {}) => {
    return mount(LoginPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute,
        },
      },
      ...options,
    })
  }

  describe('Component Rendering', () => {
    it('should render login form correctly', () => {
      const wrapper = createWrapper()

      expect(wrapper.find('h2.page-ttl').exists()).toBe(true)
      expect(wrapper.find('form').exists()).toBe(true)
      expect(wrapper.find('input[type="email"]').exists()).toBe(true)
      expect(wrapper.find('input[type="password"]').exists()).toBe(true)
      expect(wrapper.find('input[type="submit"]').exists()).toBe(true)
    })

    it('should render registration links', () => {
      const wrapper = createWrapper()

      const registerLinks = wrapper.findAll('.register-btt')
      expect(registerLinks).toHaveLength(2)
      expect(registerLinks[0].text()).toBe('新規会員登録')
      expect(registerLinks[1].text()).toBe('簡単登録（Cognito）')
    })

    it('should not show errors initially', () => {
      const wrapper = createWrapper()

      expect(wrapper.find('.id-pass-err').exists()).toBe(false)
    })
  })

  describe('Form Validation', () => {
    it('should validate required fields', async () => {
      const wrapper = createWrapper()
      const submitButton = wrapper.find('input[type="submit"]')

      await submitButton.trigger('click')
      await nextTick()

      expect(wrapper.find('.id-pass-err').exists()).toBe(true)
      expect(mockCognitoAuth.login).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('invalid-email')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(wrapper.find('.err-txt').exists()).toBe(true)
      expect(mockCognitoAuth.login).not.toHaveBeenCalled()
    })

    it('should require terms agreement', async () => {
      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      // Don't check the agreement checkbox

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(wrapper.find('.err-txt').exists()).toBe(true)
      expect(mockCognitoAuth.login).not.toHaveBeenCalled()
    })
  })

  describe('Login Flow', () => {
    it('should handle successful login', async () => {
      mockCognitoAuth.login.mockResolvedValue({type: 'SUCCESS'})

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(mockCognitoAuth.login).toHaveBeenCalledWith('<EMAIL>', 'password123')
      expect(mockRouter.replace).toHaveBeenCalledWith('/top')
    })

    it('should handle redirect after successful login', async () => {
      mockCognitoAuth.login.mockResolvedValue({type: 'SUCCESS'})
      mockRoute.query.redirect = '/dashboard'

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard')
    })

    it('should handle new password required', async () => {
      mockCognitoAuth.login.mockResolvedValue({
        type: 'NEW_PASSWORD_REQUIRED',
        message: '初回ログイン時はパスワードの変更が必要です。',
      })

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(wrapper.find('.id-pass-err').exists()).toBe(true)
      expect(wrapper.find('.err-txt').text()).toContain(
        '初回ログイン時はパスワードの変更が必要です。'
      )
    })

    it('should handle disabled user error', async () => {
      const error = new Error('このアカウントは無効化されています。管理者にお問い合わせください。')
      error.message = 'User is disabled'
      mockCognitoAuth.login.mockRejectedValue(error)

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(wrapper.find('.id-pass-err').exists()).toBe(true)
      expect(wrapper.find('.err-txt').text()).toContain('このアカウントは無効化されています')
    })

    it('should handle authentication failure', async () => {
      const error = new Error('ログインIDまたはパスワードが正しくありません。')
      error.message = 'NotAuthorizedException'
      mockCognitoAuth.login.mockRejectedValue(error)

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('wrongpassword')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(wrapper.find('.id-pass-err').exists()).toBe(true)
      expect(wrapper.find('.err-txt').text()).toContain(
        'ログインIDまたはパスワードが正しくありません'
      )
    })
  })

  describe('Navigation', () => {
    it('should navigate to register page', async () => {
      const wrapper = createWrapper()
      const registerLink = wrapper.findAll('.register-btt')[0]

      await registerLink.trigger('click')

      expect(mockRouter.push).toHaveBeenCalledWith('/register')
    })

    it('should navigate to cognito register page', async () => {
      const wrapper = createWrapper()
      const cognitoRegisterLink = wrapper.findAll('.register-btt')[1]

      await cognitoRegisterLink.trigger('click')

      expect(mockRouter.push).toHaveBeenCalledWith('/cognito-register')
    })
  })

  describe('Loading State', () => {
    it('should show loading state during login', async () => {
      let resolveLogin
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      mockCognitoAuth.login.mockReturnValue(loginPromise)

      const wrapper = createWrapper()
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')
      const agreeCheckbox = wrapper.find('input[type="checkbox"]')
      const submitButton = wrapper.find('input[type="submit"]')

      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await agreeCheckbox.setChecked(true)

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      await nextTick()

      expect(submitButton.attributes('value')).toBe('処理中...')
      expect(submitButton.attributes('disabled')).toBeDefined()

      resolveLogin({type: 'SUCCESS'})
      await nextTick()

      expect(submitButton.attributes('value')).toBe('ログイン')
    })
  })
})
