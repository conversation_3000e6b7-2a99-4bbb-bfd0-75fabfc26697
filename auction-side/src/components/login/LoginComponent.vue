<!-- <script setup>
  import {computed, ref} from 'vue'
  import {RouterLink, useRoute, useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {aesDecrypt, aesEncrypt} from '../../composables/common.js'
  import useApi from '../../composables/useApi.js'
  import {
    API_PATH,
    PATH_NAME,
    PATTERN,
    loginNextStep,
  } from '../../defined/const.js'
  import useChangeLanguage from '@/stores/language'
  import ChangePassword from './ChangePassword.vue'

  const auth = useAuthStore()
  const {changeLanguage} = useChangeLanguage()
  const {apiExecute, parseHtmlResponseError} = useApi()

  const openChangePasswordModal = ref(false)
  const tempToken = ref(null)
  const email = ref('')
  const password = ref(null)
  const saveLoginInfoFlag = ref(false)
  const route = useRoute()
  const router = useRouter()
  const errorMsg = ref('')
  const {t: translate} = useLocale()

  const errClass = 'ime-dis err'
  const {VITE_LOCALSTORAGE_LOGIN_INFO_LABEL} = import.meta.env

  if (localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL]) {
    const loginData = aesDecrypt(
      localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL]
    )

    if (loginData?.user_id && loginData.password) {
      email.value = loginData.user_id
      password.value = loginData.password
      saveLoginInfoFlag.value = true
    }
  }

  const checkInput = computed(() => {
    const check = {
      email: {pass: false, errMsg: 'メールアドレスを確認してください。'},
      password: {pass: false, errMsg: 'パスワードを確認してください。'},
    }
    if (PATTERN.EMAIL.test(email.value)) {
      check.email.pass = true
      check.email.errMsg = ''
    }
    if (password.value !== '') {
      check.password.pass = true
      check.password.errMsg = ''
    }
    return check
  })

  const isLoginDisabled = computed(() => {
    return !(checkInput.value.email.pass && checkInput.value.password.pass)
  })

  const sendRequest = async () => {
    tempToken.value = null
    openChangePasswordModal.value = false
    const loginData = {
      user_id: email.value,
      password: password.value,
    }
    await apiExecute(API_PATH.LOGIN, loginData)
      .then(async data => {
        const nextStep = data.next_step
        // Go to change password page if the first login
        if (nextStep === loginNextStep.PASSWORD_CHANGE) {
          tempToken.value = data.token
          openChangePasswordModal.value = true
        } else {
          // Login success
          auth.setToken(data.token)
          auth.setNickname(data.user_name)
          if (data.language) {
            await changeLanguage(data.language)
          }
          if (saveLoginInfoFlag.value) {
            localStorage[import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL] =
              aesEncrypt(loginData)
          } else {
            localStorage.removeItem(
              import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL
            )
          }
          router.replace(route.redirectedFrom?.path ?? PATH_NAME.TOP)
        }
      })
      .catch(error => {
        errorMsg.value = parseHtmlResponseError(error)?.message
      })
  }
</script>
<template>
  <section id="login">
    <h1 class="mb0">{{ translate('login.title') }}</h1>
    <div class="container">
      <section id="login-form">
        <ChangePassword v-model="openChangePasswordModal" :token="tempToken" />
        <form id="loginForm">
          <table class="tbl-login">
            <tbody>
              <tr>
                <th>{{ translate('login.email') }}<em class="req">※</em></th>
                <td>
                  <input
                    id="email"
                    type="text"
                    :class="
                      email === '' || checkInput.email.pass
                        ? 'ime-dis'
                        : errClass
                    "
                    required
                    v-model="email"
                  />
                  <p
                    v-show="email !== '' && !checkInput.email.pass"
                    class="err-txt"
                  >
                    {{ checkInput.email.errMsg }}
                  </p>
                </td>
              </tr>
              <tr>
                <th>{{ translate('login.password') }}<em class="req">※</em></th>
                <td>
                  <input
                    id="password"
                    type="password"
                    :class="
                      password === '' || checkInput.password.pass
                        ? 'ime-dis'
                        : errClass
                    "
                    :placeholder="translate('login.passwordHint')"
                    required
                    v-model="password"
                  />
                  <p
                    v-show="password !== '' && !checkInput.password.pass"
                    class="err-txt"
                  >
                    {{ checkInput.password.errMsg }}
                  </p>
                </td>
              </tr>
              <tr>
                <th class="only_pc">&nbsp;</th>
                <td class="check-idpass">
                  <label>
                    <input
                      type="checkbox"
                      class="checkbox-input"
                      v-model="saveLoginInfoFlag"
                    />
                    <span class="checkbox-parts">{{
                      translate('login.saveLoginInfo')
                    }}</span>
                  </label>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="forget-pass">
            <RouterLink :to="PATH_NAME.REMINDER">{{
              translate('login.forgetPassword')
            }}</RouterLink>
          </div>
          <div v-if="errorMsg && errorMsg.length > 0" class="id-pass-err">
            <p class="err-txt">
              {{ errorMsg }}
            </p>
          </div>
          <div class="btn-form">
            <input
              type="button"
              :value="translate('login.confirmButton')"
              @click="sendRequest"
              :disabled="isLoginDisabled"
            />
          </div>
        </form>
        <div class="request">
          <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
            {{ translate('login.entryInfo1') }}
          </RouterLink>
          <p>※{{ translate('login.entryInfo2') }}</p>
        </div>
      </section>
    </div>
  </section>
</template> -->
