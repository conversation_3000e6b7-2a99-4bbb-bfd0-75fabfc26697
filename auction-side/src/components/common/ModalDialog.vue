<script setup>
  import $ from 'jquery'
  import {watch, defineProps} from 'vue'

  defineProps({
    width: {
      type: String,
      default: 'auto',
    },
    showTopCloseButton: {
      type: Boolean,
      default: true,
    },
  })
  const emit = defineEmits(['refresh-on-close'])

  const open = defineModel()

  const onOpenModal = () => {
    open.value = true
    // モーダルが開くときにbodyのスクロールを禁止
    // const srollY = window.scrollY
    // $('html').css('--v-body-scroll-x', '0px')
    // $('html').css('--v-body-scroll-y', `${srollY}px`)
    // $('html').addClass('v-overlay-scroll-blocked')
  }
  const onCloseModal = () => {
    open.value = false
    emit('refresh-on-close')
  }

  watch(
    () => open.value,
    value => {
      if (value) {
        onOpenModal()
      } else {
        onCloseModal()
      }
    }
  )
</script>
<template>
  <!-- <div class="place-modal"> -->
  <div class="modal-container" v-bind:class="{active: open}">
    <div class="modal-body" :style="{width}">
      <div v-show="showTopCloseButton" class="modal-close" @click="onCloseModal">x</div>
      <div class="modal-content">
        <slot></slot>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<style lang="css" scoped>
  /* --------------------------- */
  /* TODO: This style from GEO, please change style to match the design system of saas */
  .modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 40px 20px;
    overflow: auto;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    z-index: 100;
  }
  .modal-container:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 100%;
  }
  .modal-container.active {
    opacity: 1;
    visibility: visible;
  }
  .modal-container .modal-body {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    max-width: calc(100% - 2rem);
    width: 900px;
    margin: 0 auto;
  }
  .modal-container .modal-body .modal-close {
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    top: -24px;
    right: -24px;
    width: 60px;
    height: 60px;
    font-size: 22px;
    color: #fff;
    line-height: 1;
    background-color: #929392;
    border-radius: 30px;
    cursor: pointer;
    z-index: 120;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-close {
      width: 50px;
      height: 50px;
    }
  }
  .modal-container .modal-body .modal-content {
    position: relative;
    padding: 3rem;
    background-color: #fff;
    border-radius: 10px;
    z-index: 110;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content {
      padding: 6vw;
      font-size: 3.5vw;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap {
    width: 100%;
    height: auto;
    max-height: 400px;
    overflow: auto;
    border: 1px solid #ccc;
    scrollbar-width: thin;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap {
      max-height: 78vw;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap {
    width: 100%;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap {
      margin: 0;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    padding: 0.5rem;
    background-color: #d7d7d7;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head {
      padding: 3vw 0.5rem;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .ttl-auction {
    margin: 0 0 0.5rem;
    padding: 0;
    font-size: 1rem;
    font-weight: bold;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .ttl-auction {
      margin: 0 0 1vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 0;
    padding: 0;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule {
      margin: 0;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100%;
    margin: 0 auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule
      .sch-row {
      width: 100%;
      margin: 0;
      padding: 0;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .country,
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .cont {
    padding: 3px 0;
    font-size: 13px;
    line-height: 1.1;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule
      .sch-row
      .country,
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule
      .sch-row
      .cont {
      font-size: 3vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .country {
    width: 120px;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule
      .sch-row
      .country {
      width: 14vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .country
    .wic {
    position: relative;
    display: inline-block;
    padding: 0;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .cont {
    width: calc(100% - 120px);
    white-space: wrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      .matching-head
      .schedule
      .sch-row
      .cont {
      width: calc(100% - 14vw);
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .cont
    .date {
    display: inline-block;
    margin: 0 0.5rem;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .cont
    .time {
    display: inline-block;
    margin: 0 0.5rem;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    .matching-head
    .schedule
    .sch-row
    .cont
    .symbol {
    display: inline-block;
    margin: 0 0.5rem;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table {
    width: 100%;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table {
      border-bottom: none;
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead {
      display: none;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th {
    min-width: 78px;
    padding: 9px 10px;
    color: #000;
    font-size: 10px;
    line-height: 1.2;
    text-align: center;
    vertical-align: middle;
    background-color: #eee;
    border-bottom: 1px solid #aeaeae;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th {
      font-size: 2.4vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    thead
    tr
    th.t-left {
    text-align: left;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    thead
    tr
    th.num {
    white-space: nowrap;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody {
    width: 100%;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr {
    display: table-row;
    border-bottom: 1px solid #aeaeae;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr {
      display: block;
      width: 100%;
      margin: 0;
      padding: 2vw 0.5rem;
      border-top: 1px solid #747474;
      border-bottom: none;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr:last-child {
    border-bottom: 1px solid #747474;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr:last-child {
      border-bottom: 1px solid #747474;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td {
    padding: 11px 10px;
    font-size: 13px;
    text-align: center;
    line-height: 1.2;
    vertical-align: middle;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td {
      width: 100%;
      position: relative;
      display: block;
      padding: 5px 0;
      font-size: 3vw;
      text-align: right;
      border-bottom: 1px solid #e4e4e4;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td:before {
    position: absolute;
    left: 0;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.maker {
    max-width: 140px;
    text-align: left;
    -webkit-box-flex: 2;
    -ms-flex-positive: 2;
    flex-grow: 2;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.maker {
      max-width: 100%;
      padding: 8px 0 8px 5em;
      text-align: right;
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.maker:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.itemname {
    padding: 11px 10px 11px 10px;
    text-align: left;
    -webkit-box-flex: 2;
    -ms-flex-positive: 2;
    flex-grow: 2;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.itemname {
      padding: 8px 0 8px 5em;
      text-align: right;
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.itemname:before {
      content: '';
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.sim:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.capacity {
    white-space: nowrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.capacity:before {
      content: '';
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.color:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.grade {
    white-space: nowrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.grade:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.quantity {
    white-space: nowrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.quantity:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.quantity-min {
    white-space: nowrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.quantity-min:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.price-min {
    font-family: 'sans-serif', 'system-ui';
    white-space: nowrap;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.price-min:before {
      content: '';
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check {
    position: relative;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check:after {
    content: '';
    position: absolute;
    top: 15%;
    left: 0;
    width: 1px;
    height: 70%;
    background: #e3e3e3;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.check:after {
      display: none;
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.check {
      border-bottom: none;
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-wrap
      table
      tbody
      tr
      td.check:before {
      content: '';
      top: 10px;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check
    button.fav-mark {
    background: transparent;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check
    button.fav-mark
    .fav-pct {
    width: 24px;
    height: 24px;
    background-image: url(../img/common/icn_favorite_gr.svg);
    background-size: 24px 24px;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check
    button.fav-mark
    .fav-pct:hover {
    -webkit-transform: scale(1.15);
    transform: scale(1.15);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td.check
    button.fav-mark
    .fav-pct.added {
    background-image: url(../img/common/icn_favorite.svg);
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-wrap
    table
    tbody
    tr
    td:last-child {
    border-bottom: none;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td a {
    color: #0084c1;
    font-weight: 500;
    line-height: 1.2;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.5rem;
    border-bottom: 2px solid #747474;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .ttl {
    width: 60%;
    font-size: 13px;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .ttl {
      font-size: 3vw;
      line-height: 1.2;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .pri {
    width: calc(40% - 1vw);
    padding: 0 0 0 1vw;
    line-height: 1.2;
    text-align: right;
    font-size: 13px;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.8rem 0.5rem;
    background-color: #faf1f2;
    border-bottom: 2px solid #747474;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction {
      padding: 1.1rem 0.5rem;
      border-bottom: 2px solid #747474;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .total-bid-amount
    .transaction
    .ttl {
    width: 60%;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .total-bid-amount
      .transaction
      .ttl {
      font-size: 3vw;
      line-height: 1.2;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .total-bid-amount
    .transaction
    .pri {
    width: calc(40% - 1vw);
    padding: 0 0 0 1vw;
    line-height: 1.2;
    text-align: right;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap {
    padding: 1rem;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap {
      padding: 0.5rem;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-ttl {
    margin: 0 0 0.3rem;
    padding: 0;
    font-size: 14.4px;
    font-weight: bold;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-ttl {
      font-size: 3vw;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list {
    border-top: 1px solid #ccc;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li {
    padding: 0.3rem 0;
    border-bottom: 1px solid #ccc;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-line-wrap
      ul.bid-list
      li {
      padding: 0.3rem 0 0.5rem;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p {
    width: 100%;
    font-size: 14.4px;
    line-height: 1.4;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-line-wrap
      ul.bid-list
      li
      p {
      font-size: 3vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p.bid-item-name {
    border: none;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p.bid-item-name
    span {
    margin: 0 1rem 0 0;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p
    span {
    display: inline-block;
    margin: 0 0.5rem 0 0;
    font-family: 'sans-serif', 'system-ui';
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p
    span.label {
    position: relative;
    margin: 0 10px 0 0;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    p
    span.label:after {
    position: absolute;
    right: -7px;
    content: ':';
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion {
    font-size: 14.4px;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-line-wrap
      ul.bid-list
      li
      div.criterion {
      font-size: 3vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion
    dl {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    line-height: 1.4;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-line-wrap
      ul.bid-list
      li
      div.criterion
      dl {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion
    dl
    dt {
    min-width: 4rem;
    margin-right: 0.7rem;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion
    dl.success
    dt {
    color: #008ece;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion
    dl.failure
    dt {
    color: #ff0000;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    ul.bid-list
    li
    div.criterion
    dl.failure
    dd {
    color: #ff0000;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-total {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin: 0.3rem 0 0;
    font-size: 14.4px;
  }
  @media screen and (max-width: 767px) {
    .modal-container
      .modal-body
      .modal-content
      .matching-dir-wrap
      .auction-line-wrap
      .auction-total {
      font-size: 3vw;
    }
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    .auction-total
    .label {
    margin: 0 1rem 0 0;
  }
  .modal-container
    .modal-body
    .modal-content
    .matching-dir-wrap
    .auction-line-wrap
    .auction-total
    .price {
    font-family: 'sans-serif', 'system-ui';
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin: 1.5rem 0 0;
    padding: 1.3rem 1rem;
    font-weight: 600;
    border-top: 2px solid #ccc;
    border-bottom: 2px solid #ccc;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount {
      padding: 1rem 0.5rem;
      font-size: 3.5vw;
    }
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount p {
    font-weight: 600;
  }
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount p.price {
    font-family: 'sans-serif', 'system-ui';
  }
  .modal-container .modal-body .modal-content .error-contents-wrap {
    margin: 0 0 1rem;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-item {
    padding: 0.5rem 1rem;
    background-color: #f5f5f5;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-item p.item-head span {
    margin: 0 1rem 0 0;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-item p span {
    display: inline-block;
    margin: 0 0.5rem 0 0;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-item p span.label {
    position: relative;
    margin: 0 10px 0 0;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-item p span.label:after {
    position: absolute;
    right: -7px;
    content: ':';
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-message {
    padding: 0.5rem 1rem;
  }
  .modal-container .modal-body .modal-content .error-contents-wrap .error-message .error {
    margin: 0.5rem 0;
    color: #ff0000;
    text-align: center;
    line-height: 1.4;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .error-contents-wrap .error-message .error {
      margin: 0;
      font-size: 3vw;
    }
  }
  .modal-container .modal-body .modal-content .rule {
    width: 820px;
    max-width: 100%;
    margin: 20px auto;
  }
  .modal-container .modal-body .modal-content .rule p {
    padding: 0 0.3rem 0.2rem;
    font-size: 0.9rem;
    font-weight: nomal;
    text-align: center;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .rule p {
      font-size: 3.5vw;
    }
  }
  .modal-container .modal-body .modal-content .rule embed {
    width: 100%;
    height: 100px;
    border: 1px solid #ccc;
    scrollbar-width: thin;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .modal-container .modal-body .modal-content .rule .rule-check {
    margin: 0.5rem 0 0;
    text-align: center;
  }
  .modal-container .modal-body .modal-content .note-bid {
    width: 100%;
    padding: 0 1rem;
    font-size: 12px;
    text-align: center;
  }
  .modal-container .modal-body .modal-content .button-bid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    height: auto;
  }
  .modal-container .modal-body .modal-content .button-bid button {
    width: 280px;
    max-width: calc(100% - 2rem);
    height: 56px;
    margin: 0.5rem auto 0;
    color: #fff;
    font-size: 1rem;
    font-weight: 500;
    background-color: #e60012;
    border-radius: 4px;
  }
  @media screen and (max-width: 1080px) {
    .modal-container .modal-body .modal-content .button-bid button {
      width: auto;
      min-width: calc(100% - 2rem);
    }
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .button-bid button {
      width: calc(100% - 2rem);
    }
  }
  .modal-container .modal-body .modal-content .button-bid button[disabled] {
    background-color: #814340;
    color: #918281;
  }
  .modal-container .modal-body .modal-content .button-modal-close {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    height: auto;
  }
  .modal-container .modal-body .modal-content .button-modal-close button {
    width: 280px;
    max-width: calc(100% - 2rem);
    height: 56px;
    margin: 0.5rem auto 0;
    color: #fff;
    font-weight: 500;
    background-color: #444;
    border-radius: 4px;
  }
  @media screen and (max-width: 767px) {
    .modal-container .modal-body .modal-content .button-modal-close button {
      width: calc(100% - 2rem);
      font-size: 3.8vw;
    }
  }
  /*# sourceMappingURL=parts.css.map */
</style>
