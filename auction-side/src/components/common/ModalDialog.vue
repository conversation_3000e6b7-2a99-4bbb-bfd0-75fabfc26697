<script setup>
  import $ from 'jquery'
  import {watch, defineProps} from 'vue'

  defineProps({
    width: {
      type: String,
      default: 'auto',
    },
    showTopCloseButton: {
      type: Boolean,
      default: true,
    },
  })
  const emit = defineEmits(['refresh-on-close'])

  const open = defineModel()

  const onOpenModal = () => {
    open.value = true
    // モーダルが開くときにbodyのスクロールを禁止
    // const srollY = window.scrollY
    // $('html').css('--v-body-scroll-x', '0px')
    // $('html').css('--v-body-scroll-y', `${srollY}px`)
    // $('html').addClass('v-overlay-scroll-blocked')
  }
  const onCloseModal = () => {
    open.value = false
    emit('refresh-on-close')
  }

  watch(
    () => open.value,
    value => {
      if (value) {
        onOpenModal()
      } else {
        onCloseModal()
      }
    }
  )
</script>
<template>
  <div class="modal-container" v-bind:class="{active: open}">
    <div class="modal-body" :style="{width}">
      <div v-show="showTopCloseButton" class="modal-close" @click="onCloseModal">×</div>
      <div class="modal-content common-modal-here">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
