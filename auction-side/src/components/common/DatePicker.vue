<script setup>
  import {format, getDaysInMonth, isValid} from 'date-fns'
  import {computed, defineEmits, defineProps, onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'

  const emit = defineEmits(['update:value'])
  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
  })

  const {t} = useLocale()

  const year = ref('')
  const month = ref('')
  const day = ref('')

  const currentYear = new Date().getFullYear()
  const years = Array.from({length: 100}, (_, i) => currentYear - i)
  const months = Array.from({length: 12}, (_, i) => i + 1)
  const days = computed(() => {
    if (!year.value || !month.value) {
      return []
    }
    const date = new Date(year.value, month.value, 0)
    return Array.from({length: getDaysInMonth(date)}, (_, i) => i + 1)
  })

  const reset = newValue => {
    const date = new Date(newValue)
    if (!isValid(date)) {
      return
    }
    year.value = date.getFullYear()
    month.value = date.getMonth() + 1
    day.value = date.getDate()
  }

  onMounted(() => {
    reset(props.value)
  })

  watch(
    () => props.value,
    newValue => {
      reset(newValue)
    }
  )

  const onChange = () => {
    if (!year.value || !month.value || !day.value) {
      emit('update:value', '')
      return
    }
    const date = new Date(year.value, month.value, 0)
    const lastDay = getDaysInMonth(date)
    if (lastDay < day.value) {
      day.value = lastDay
    }
    date.setDate(day.value)
    emit('update:value', format(date, 'yyyy-MM-dd'))
  }
</script>
<template>
  <div class="select-col">
    <div>
      <!-- 年選択 -->
      <select name="year" class="" v-model="year" @change="onChange">
        <option value="">{{ t('DATE_PICKER_YEAR') }}</option>
        <option v-for="y in years" :key="y" :value="y">{{ y }}</option>
      </select>
    </div>

    <div>
      <!-- 月選択 -->
      <select name="month" class="" v-model="month" @change="onChange">
        <option value="">{{ t('DATE_PICKER_MONTH') }}</option>
        <option v-for="m in months" :key="m" :value="m">{{ m }}</option>
      </select>
    </div>

    <div>
      <!-- 日選択 -->
      <select name="day" class="" v-model="day" @change="onChange">
        <option value="">{{ t('DATE_PICKER_DAY') }}</option>
        <option v-for="d in days" :key="d" :value="d">{{ d }}</option>
      </select>
    </div>
  </div>
</template>
