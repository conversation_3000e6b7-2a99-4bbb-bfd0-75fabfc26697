import {fetchAuthSession as amplifyFetchAuthSession, signIn, signOut} from '@aws-amplify/auth'
import {Hub} from 'aws-amplify/utils'
import {jwtDecode} from 'jwt-decode'
import {defineStore} from 'pinia'
import {computed, ref} from 'vue'
import {useRouter} from 'vue-router'
import useApi from '../composables/useApi'

interface LoginHistoryMemberInfo {
  member_no: number
  user_no: number
  user_id: string
  member_id: string
  free_field: {
    email: string
    language: string
    memberName: string
  }
}

interface LoginHistoryResponse {
  success: boolean
  memberInfo: LoginHistoryMemberInfo
  message: string
}

interface CognitoUser {
  email: string
  memberNo: string
  userNo: string
  memberName: string
  languageCode: string
  tenantId: string
}

interface LoginResult {
  type: 'SUCCESS' | 'NEW_PASSWORD_REQUIRED' | 'EMAIL_VERIFICATION_REQUIRED' | 'ERROR'
  message?: string
}

interface LoginHistoryResult {
  success: boolean
  error?: string
}

// Translates Cognito error objects into Japanese messages
const errorToMessage = (error: any): string => {
  if (!error) return '不明なエラーが発生しました。'

  if (error.message && error.message.includes('User is disabled.')) {
    return 'このアカウントは無効化されています。管理者にお問い合わせください。'
  }

  switch (error.name) {
    case 'UserNotConfirmedException':
      return 'このアカウントは有効性が検証されていません。'
    case 'UserNotFoundException':
      return 'ログインIDまたはパスワードが正しくありません。'
    case 'NotAuthorizedException':
      return 'ログインIDまたはパスワードが正しくありません。'
    case 'UserAlreadyAuthenticatedException':
      return '既にログインしています。'
    case 'LimitExceededException':
      return 'ログイン試行回数が制限を超えました。しばらくしてから再度お試しください。'
    case 'InvalidPasswordException':
      return 'パスワードはポリシーの要件を満たしていません。'
    case 'TooManyRequestsException':
      return 'リクエストが多すぎます。しばらくしてから再度お試しください。'
    default:
      return error.message || 'ログインに失敗しました。入力情報を確認してください。'
  }
}

// Extended JWT payload interface for Cognito tokens
interface CognitoJwtPayload {
  email: string
  'custom:member_no': string
  'custom:user_no': string
  'custom:member_name': string
  'custom:language_code': string
  'tenant-id': string
  [key: string]: any
}

export const useCognitoAuthStore = defineStore('cognitoAuth', () => {
  const router = useRouter()
  const {apiExecute} = useApi()

  const user = ref<CognitoUser | null>(null)
  const idToken = ref<string | null>(null)
  const accessToken = ref<string | null>(null)
  const memberInfo = ref<LoginHistoryMemberInfo | null>(null)
  const isAuthenticated = computed(() => !!user.value && !!idToken.value)

  function _setUserFromToken(token: string | null) {
    if (!token) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      return
    }

    const decodedToken = jwtDecode<CognitoJwtPayload>(token)
    user.value = {
      email: decodedToken.email,
      memberNo: decodedToken['custom:member_no'],
      userNo: decodedToken['custom:user_no'],
      memberName: decodedToken['custom:member_name'],
      languageCode: decodedToken['custom:language_code'],
      tenantId: decodedToken['tenant-id'],
    }
    idToken.value = token
  }

  async function fetchAuthSession(forceRefresh: boolean = false): Promise<boolean> {
    try {
      const session = await amplifyFetchAuthSession({forceRefresh})
      const token = session.tokens?.idToken?.toString()
      if (!token) {
        throw new Error('No token found in session')
      }
      _setUserFromToken(token)
      accessToken.value = session.tokens?.accessToken?.toString() || null
      return true
    } catch (e) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      memberInfo.value = null
      return false
    }
  }

  async function logLoginHistory(): Promise<LoginHistoryResult> {
    try {
      const result = await apiExecute<LoginHistoryResponse>(
        'private/login-history-logging',
        {
          languageCode: 'ja',
        },
        false, // not a file upload
        {
          authorization: `Bearer ${idToken.value}`,
        }
      )
      console.log('Login history logged successfully')

      // Store the memberInfo from the response
      if (result.success && result.memberInfo) {
        memberInfo.value = result.memberInfo
      }

      return {success: true}
    } catch (error: any) {
      console.warn('Login history logging error:', error)

      const errorMessage = error?.response?.data?.message || 'ログイン履歴の記録に失敗しました。'
      return {success: false, error: errorMessage}
    }
  }

  async function login(email: string, password: string): Promise<LoginResult> {
    try {
      const signInResponse = await signIn({
        username: email,
        password,
        options: {
          authFlowType: 'USER_PASSWORD_AUTH',
        },
      })

      if (signInResponse.isSignedIn) {
        await fetchAuthSession()

        // Log login history and check for backend errors
        const loginResult = await logLoginHistory()
        if (!loginResult.success && loginResult.error) {
          // Backend validation error - show error to user
          await logout()
          throw new Error(loginResult.error)
        }

        return {type: 'SUCCESS'}
      }

      const {signInStep} = signInResponse.nextStep
      switch (signInStep) {
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            type: 'NEW_PASSWORD_REQUIRED',
            message: '初回ログイン時はパスワードの変更が必要です。',
          }
        case 'CONFIRM_SIGN_UP':
          return {
            type: 'EMAIL_VERIFICATION_REQUIRED',
            message: 'メールアドレスの確認が必要です。',
          }
        case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':
          return {
            type: 'EMAIL_VERIFICATION_REQUIRED',
            message: 'メール認証コードの入力が必要です。',
          }
        default:
          return {
            type: 'ERROR',
            message: `未対応の認証ステップです: ${signInStep}`,
          }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      throw new Error(errorToMessage(error))
    }
  }

  async function logout(): Promise<void> {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      idToken.value = null
      accessToken.value = null
      memberInfo.value = null
      router.push('/login')
    }
  }

  Hub.listen('auth', ({payload: {event}}) => {
    switch (event) {
      case 'signedIn':
        fetchAuthSession()
        break
      case 'signedOut':
      case 'tokenRefresh_failure':
        user.value = null
        idToken.value = null
        accessToken.value = null
        memberInfo.value = null
        break
    }
  })

  return {
    user,
    idToken,
    accessToken,
    // memberInfo, 今、使わないのでコメントアウトする
    isAuthenticated,
    fetchAuthSession,
    login,
    logout,
  }
})
