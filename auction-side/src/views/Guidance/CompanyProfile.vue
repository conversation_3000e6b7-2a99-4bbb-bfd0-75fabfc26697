<script setup>
  import {onBeforeMount, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'

  const {current} = useLocale()
  const currentLocale = ref(current.value)

  watch(
    () => current.value,
    () => {
      currentLocale.value = current.value
    }
  )

  const initData = async () => {
    console.log('Loading data from backend')
  }

  onBeforeMount(async () => {
    await initData()
  })
</script>

<template>
  <div>Profile here!!!</div>
</template>
