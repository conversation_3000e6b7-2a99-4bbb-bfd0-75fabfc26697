<script setup>
  import {onMounted} from 'vue'
  import {scrollToAnker} from '../../../composables/common'
  // const PageTopLink = defineAsyncComponent(
  //   () => import(/* webpackChunkName: "PageTopLink" */ '../../../components/parts/PageTopLink.vue')
  // )

  onMounted(() => {
    console.log('mounted EN')
    $('a[href^="#"]').click(function () {
      const href = $(this).attr('href')
      const hash = href === '#' || href === '' ? 'html' : href
      scrollToAnker(hash)
      return false
    })
  })
</script>
<template>
  <div>
    <PageTopLink />
    <h1 class="mb0">Auction User's Guide</h1>
    <section id="beginner">
      <div class="container">
        <div class="table-contents">
          <h2>How to Use</h2>
          <div class="list-wrap">
            <ul>
              <li>
                <a href="#guide-ta01"><span class="stp">STEP 1</span> New Member Registration</a>
              </li>
              <li>
                <a href="#guide-ta02"><span class="stp">STEP 2</span> Online Bidding</a>
              </li>
              <li>
                <a href="#guide-ta03"><span class="stp">STEP 3</span> Auction Results</a>
              </li>
              <li>
                <a href="#guide-ta04"><span class="stp">STEP 4</span> Payment</a>
              </li>
              <li>
                <a href="#guide-ta05"><span class="stp">STEP 5</span> Product Delivery</a>
              </li>
            </ul>
          </div>
        </div>
        <ul class="step">
          <li class="step-1" id="guide-ta01">
            <h3><em>STEP 1</em> New Member Registration</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li>
                      <span class="sym">01.</span> Please apply from "New Member Registration" under
                      "Membership Information" in the header menu.
                    </li>
                    <li>
                      <span class="sym">02.</span> After completing the registration, we will
                      contact you via email.
                    </li>
                    <li>
                      <span class="sym">03.</span> Sign in using your registered email address and
                      password from the sign in button at the top right of the header menu.
                    </li>
                  </ul>
                  <ul class="ann">
                    <li>※ Registration may take up to one week.</li>
                    <li>
                      ※ Japanese corporations are required to have a secondhand dealer permit.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-2" id="guide-ta02">
            <h3><em>STEP 2</em> Online Bidding</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span> Sign in and check the auction details.</li>
                    <li>
                      <span class="sym">02.</span> Enter the desired quantity and bid price (in
                      USD).
                    </li>
                  </ul>
                  <ul class="ann">
                    <li>
                      ※ There are two types of auctions: Incremental and Sealed.<br />
                      ⚫︎ Incremental Auction: The highest bid wins.<br />
                      ⚫︎ Sealed Auction: Winning priority is determined by [Bid Price ⇒ Bid
                      Quantity ⇒ Bid Time].
                    </li>
                    <li>※ Bid prices are tax-excluded.</li>
                    <li>※ You can also register favorites and place bulk bids from My Page.</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-3" id="guide-ta03">
            <h3><em>STEP 3</em> Auction Results</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span> Check the auction results from My Page.</li>
                  </ul>
                  <ul class="ann">
                    <li>※ We will notify you of the auction results via email.</li>
                    <li>※ Invoices or billing statements will be sent via email.</li>
                    <li>
                      ※ Results for auctions you did not bid on can be viewed under "Auction
                      Results" after logging in.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-4" id="guide-ta04">
            <h3><em>STEP 4</em> Payment</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li>
                      <span class="sym">01.</span> Please make the payment within three business
                      days after receiving the invoice.
                    </li>
                  </ul>
                  <ul class="ann">
                    <li>※ Bank account details are listed on the invoice or billing statement.</li>
                    <li>
                      ※ If paying in JPY, exchange rates will be announced under "Notices" before
                      the auction starts.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-5" id="guide-ta05">
            <h3><em>STEP 5</em> Product Delivery</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li>
                      <span class="sym">01.</span> Once the payment is confirmed, we will proceed
                      with shipping.
                    </li>
                  </ul>
                  <ul class="ann">
                    <li>※ For international transfers, payment confirmation may take some time.</li>
                    <li>※ We will notify you once the product has been shipped.</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
