import {createRouter, createWebHistory} from 'vue-router'
import {PATH_NAME} from '../defined/const'

import AuctionDetails from '@/components/auction/detail/AuctionDetails.vue'
import CognitoRegister from '@/components/register/CognitoRegister.vue'
import {useCognitoAuthStore} from '@/stores/cognitoAuth.ts'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    // Scroll to the top position when entering a new route
    return {top: 0}
  },
  routes: [
    {
      path: PATH_NAME.TOP,
      name: 'home',
      component: () => import('@/components/top/_TopPageComponent.vue'),
      meta: {
        name: 'ROUTE_TOP',
        bodyId: 'home',
      },
    },
    {
      path: PATH_NAME.LOGIN,
      name: 'login',
      component: () => import('@/components/login/LoginPage.vue'),
      // meta: {
      //   headerShow: true,
      //   footerShow: true,
      //   name: 'route.login',
      //   bodyId: 'login',
      // },
    },
    {
      path: PATH_NAME.REMINDER,
      name: 'reminder',
      component: () => import('@/views/Member/ReminderPage.vue'),
      meta: {
        name: 'route.reminder',
        bodyId: 'reminder',
      },
    },
    {
      path: PATH_NAME.ENTRY_INFO_REGIST,
      name: 'entryinfo-regist',
      component: () => import('@/views/Member/RegisterPage.vue'),
      meta: {
        isRegist: true,
        name: 'route.register',
        bodyId: 'entry',
      },
    },
    {
      path: PATH_NAME.ENTRY_INFO_CONFIRM,
      name: 'entryinfo-complete',
      component: () => import('@/views/Member/RegisterPage.vue'),
      meta: {
        isConfirm: true,
        name: 'route.register',
        bodyId: 'entry',
      },
    },
    {
      path: PATH_NAME.COGNITO_REGISTER,
      name: 'cognito-register',
      component: CognitoRegister,
      meta: {
        name: 'route.cognitoRegister',
        bodyId: 'cognito-register',
      },
    },
    {
      path: PATH_NAME.SEARCH_RESULTS,
      name: 'search-results',
      component: () => import('@/components/auction-search-results/_SearchResults.vue'),
      meta: {
        name: 'route.auctionList',
        bodyId: 'auctionList',
      },
    },
    {
      path: PATH_NAME.NOTICE_LIST,
      name: 'list-notice',
      component: () => import('@/views/List/NoticeList.vue'),
      meta: {
        name: 'route.noticeList',
        bodyId: 'news',
      },
    },
    {
      path: PATH_NAME.NOTICE_LIST_IMPORTANT,
      name: 'list-notice-important',
      component: () => import('@/views/List/NoticeList.vue'),
      meta: {
        name: 'route.importantNoticeList',
        bodyId: 'news',
      },
    },
    // {
    //   path: PATH_NAME.BID_HISTORY_ALL,
    //   name: 'bid-history-all',
    //   component: () => import('@/views/List/BidHistoryAll.vue'),
    //   meta: {
    //     requireAuth: true,
    //     name: 'route.bidHistoryAll',
    //     bodyId: 'result',
    //   },
    // },
    {
      path: `${PATH_NAME.DETAIL}/:manageNo`,
      name: 'details',
      component: AuctionDetails,
      meta: {
        // requireAuth: true,
        name: 'route.details',
        bodyClass: 'stock',
        bodyId: 'detail',
      },
    },
    // {
    //   path: `${PATH_NAME.DETAIL}/:exhibitionItemNo/contact`,
    //   name: 'details-contact',
    //   component: () => import('@/views/contact/ContactUs.vue'),
    //   meta: {
    //     requireAuth: true,
    //     name: 'route.contact',
    //     bodyId: 'contact',
    //     mainClass: 'contact-used',
    //   },
    // },
    {
      path: `${PATH_NAME.DETAIL}/${PATH_NAME.CHAT}/:exhibitionItemNo`,
      name: 'details-chat',
      component: () => import('@/components/auction/chat/AuctionDetailChat.vue'),
      meta: {
        requireAuth: true,
        name: 'route.chat',
        bodyId: 'chat',
      },
    },
    {
      path: `${PATH_NAME.NOTICE_LIST}/:noticeNo`,
      name: 'notice-details',
      component: () => import('@/views/NoticePage.vue'),
      meta: {
        name: 'route.noticeDetails',
        bodyId: 'news',
      },
    },
    {
      path: `${PATH_NAME.DETAIL}`,
      name: 'details-makeshop',
      component: () => import('@/components/auction/detail/AuctionDetails.vue'),
      meta: {
        requireAuth: true,
        name: 'route.details',
        bodyClass: 'stock',
        bodyId: 'detail',
      },
    },
    {
      path: `${PATH_NAME.NOTICE_LIST_IMPORTANT}/:noticeNo`,
      name: 'notice-important-details',
      component: () => import('@/views/NoticePage.vue'),
      meta: {
        name: 'route.importantNotice',
        bodyId: 'news',
      },
    },
    {
      path: PATH_NAME.FAVORITES,
      name: 'favorites',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        name: 'HEADER_NAV_FAVORITES',
        bodyClass: 'mypage',
        label: 'HEADER_NAV_FAVORITES',
      },
    },
    {
      path: PATH_NAME.BIDS,
      name: 'bids',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        name: 'HEADER_NAV_BIDDING',
        bodyClass: 'mypage',
        label: 'HEADER_NAV_BIDDING',
      },
    },
    {
      path: PATH_NAME.BID_HISTORY,
      name: 'bid-history',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        name: 'HEADER_NAV_SUCCESSFUL_BID_HISTORY',
        bodyClass: 'mypage',
        label: 'HEADER_NAV_SUCCESSFUL_BID_HISTORY',
      },
    },
    {
      path: PATH_NAME.MYPAGE_EDIT,
      name: 'my-page-edit',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        isRegist: true,
        name: 'MYPAGE_EDIT_PROFILE',
        bodyId: '',
        bodyClass: 'mypage',
        label: 'user.myPage',
      },
    },
    {
      path: PATH_NAME.MYPAGE_EDIT_CONFIRM,
      name: 'my-page-confirm',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        isConfirm: true,
        name: 'MYPAGE_EDIT_CONFIRM',
        bodyId: 'entry',
        label: 'route.myPageEditConfirm',
      },
    },
    {
      path: PATH_NAME.MYPAGE_CARD,
      name: 'my-page-card',
      component: () => import('@/components/mypage/_MyPage.vue'),
      meta: {
        requireAuth: true,
        name: 'MYPAGE_CARD',
        bodyId: 'card',
        label: 'route.myPageCard',
      },
    },
    {
      path: PATH_NAME.PROFILE,
      name: 'profile',
      component: () => import('@/views/Guidance/CompanyProfile.vue'),
      meta: {
        name: 'route.companyOverview',
        bodyId: 'profile',
      },
    },
    {
      path: PATH_NAME.TERMS,
      name: 'terms',
      component: () => import('@/views/Guidance/Terms.vue'),
      meta: {
        name: 'route.terms',
        bodyId: 'terms',
      },
    },
    {
      path: PATH_NAME.PRIVACY,
      name: 'privacy',
      component: () => import('@/views/Guidance/PrivacyPage.vue'),
      meta: {
        name: 'route.privacy',
        bodyId: 'privacy',
      },
    },
    {
      path: PATH_NAME.GUIDE,
      name: 'guide',
      component: () => import('@/views/Guidance/GuidePage.vue'),
      meta: {
        name: 'route.firstTime',
        bodyId: 'guide',
      },
    },
    {
      path: PATH_NAME.ORDER_CONTRACT,
      name: 'order-contract',
      component: () => import('@/views/Guidance/OrderContract.vue'),
      meta: {
        name: 'route.toshuho',
        bodyId: 'tokushoho',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      component: () => import('@/components/top/_TopPageComponent.vue'),
    },
  ],
})

router.beforeEach(async (to, from, next) => {
  const cognitoAuth = useCognitoAuthStore()

  // Scroll to top for new routes (unless there's a hash)
  if (!to.hash) {
    window.scrollTo({top: 0})
  }

  // Try to refresh authentication session to get current state
  try {
    await cognitoAuth.fetchAuthSession()
  } catch (error) {
    console.warn('Failed to fetch auth session:', error)
  }

  // Prevent authenticated users from accessing login page
  if (cognitoAuth.isAuthenticated && to.name === 'login') {
    // Redirect to previous page or default to MyPage
    const redirectPath = from.path !== '/login' ? from.path : PATH_NAME.MYPAGE_FAVORITE
    return next({path: redirectPath})
  }

  // Protect routes that require authentication
  if (to?.meta.requireAuth && !cognitoAuth.isAuthenticated) {
    // Store the intended destination for redirect after login
    const redirectQuery = to.fullPath !== '/' ? {redirect: to.fullPath} : {}
    return next({name: 'login', query: redirectQuery})
  }

  return next()
})

/**
 * S3へデプロイ後に動的にインポートされたモジュールが取得できなかった場合のエラーハンドリング。
 * onErrorフックを使用してエラーが発生した場合にルートパス（`/`）に遷移
 * 参照: https://github.com/vitejs/vite/issues/11804#issuecomment-1406182566
 */
router.onError((error, from, to) => {
  console.log('router error:', error)
  console.log('[Router] Navigating from:', from.fullPath, '→', to.fullPath)

  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.warn(`${error.message}, force reload.`)
    // 開発環境では強制リロードを行わない（Viteの環境変数を使用）
    const isDevelopment = import.meta.env.DEV
    if (!isDevelopment) {
      window.location.href = '/'
    } else {
      console.warn('Skipping force reload in development environment')
    }
    return
  }
  console.error('Unhandled router error:', error)
})

export default router
