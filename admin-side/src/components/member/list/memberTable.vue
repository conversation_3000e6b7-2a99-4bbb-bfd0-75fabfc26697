<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header">
        <CIcon name="cil-grid" /> {{ caption }}
        <span style="float: right; margin-left: 30px"
          >総件数: {{ total_count }}件</span
        >
        <span style="float: right">検索結果: {{ current_count }}件</span>
      </slot>
    </CCardHeader>
    <CCardBody>
      <div>
        <CRow>
          <CCol
            md="2"
            class="mb-3 mb-xl-0 text-right d-grid"
            style="padding-right: 0px"
          >
            <CButton
              color="primary"
              size="sm"
              @click="addNewMember"
              block
              :disabled="isReadOnly"
              >新規追加</CButton
            >
          </CCol>
          <CCol col="2" />
          <CCol sm="5" />
        </CRow>
      </div>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <TCDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #create_datetime="{item}">
          <td class="text-center" style="min-width: 100px">
            {{ item.create_datetime }}
          </td>
        </template>
        <template #memberId="{item}">
          <td style="min-width: 100px">
            <div>{{ item.member_id }}</div>
          </td>
        </template>
        <template #last_login_datetime="{item}">
          <td class="text-left" style="min-width: 100px">
            <p
              class="mb-0"
              style="white-space: pre-line"
              v-html="item.last_login_datetime"
            ></p>
          </td>
        </template>
        <template #status="{item}">
          <td class="text-center">
            <div>{{ item.status_name }}</div>
            <div>{{ item.last_update_datetime }}</div>
          </td>
        </template>
         <template #btn_member_edit="{item}">
          <td class="text-center">
            <div class="btn-status-row" style="padding: 0.25rem;">
              <CButton
                size="sm"
                color="success"
                @click="editMember(item)"
                >編集</CButton
              >
            </div>
          </td>
        </template>
        <template #btn_edit="{item}">
          <td style="width: 140px">
            <div class="btn-status-row">
              <div class="btn-status-col">
                <CButton
                  class="btn-status"
                  size="sm"
                  color="primary"
                  @click="changeStatus(item, 1)"
                  :disabled="
                    !isChangableStatus(item.status, 1, item.member_no) ||
                    isReadOnly
                  "
                  :class="
                    !isChangableStatus(item.status, 1, item.member_no)
                      ? 'btn button btn-dark disabled'
                      : 'btn btn-primary'
                  "
                  >承認</CButton
                >
              </div>
              <div class="btn-status-col">
                <CButton
                  class="btn-status"
                  size="sm"
                  color="danger"
                  @click="changeStatus(item, 2)"
                  :disabled="
                    !isChangableStatus(item.status, 2, item.member_no) ||
                    isReadOnly
                  "
                  :class="
                    !isChangableStatus(item.status, 2, item.member_no)
                      ? 'btn button btn-dark disabled'
                      : 'btn btn-primary'
                  "
                  >非承認</CButton
                >
              </div>
            </div>
            <div class="btn-status-row">
              <div class="btn-status-col">
                <CButton
                  class="btn-status"
                  size="sm"
                  color="warning"
                  @click="changeStatus(item, 8)"
                  :disabled="
                    !isChangableStatus(item.status, 8, item.member_no) ||
                    isReadOnly
                  "
                  :class="
                    !isChangableStatus(item.status, 8, item.member_no)
                      ? 'btn button btn-dark disabled'
                      : 'btn btn-primary'
                  "
                  >停止</CButton
                >
              </div>
              <div class="btn-status-col">
                <CButton
                  class="btn-status"
                  size="sm"
                  color="danger"
                  @click="changeStatus(item, 9)"
                  :disabled="
                    !isChangableStatus(item.status, 9, item.member_no) ||
                    isReadOnly
                  "
                  :class="
                    !isChangableStatus(item.status, 9, item.member_no)
                      ? 'btn button btn-dark disabled'
                      : 'btn btn-primary'
                  "
                  >退会</CButton
                >
              </div>
            </div>
            <div class="btn-status-row">
              <div class="btn-history-col">
                <CButton
                  class="btn-history"
                  size="sm"
                  color="info"
                  @click="viewStatusHistory(item)"
                  >変更履歴表示</CButton
                >
              </div>
            </div>
          </td>
        </template>
      </TCDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script>
  import {CPagination} from '@/components/Table';
import {useAuthStore} from '@/store/auth';
import {itemsPerPageSelect} from '@/views/common/customTableView.js';
import TCDataTable from './TCDataTable.vue';

  export default {
    name: 'memberTable',
    components: {
      TCDataTable,
      CPagination,
    },
    setup() {
      const {isReadOnly} = useAuthStore();

      return {
        itemsPerPageSelect,
        isReadOnly,
      };
    },
    props: {
      items: Array,
      current_count: {
        type: String,
        default: '0',
      },
      total_count: {
        type: String,
        default: '0',
      },
      fields: {
        type: Array,
        default() {
          return [
            {
              key: 'create_datetime',
              label: '登録日',
              _style: 'text-align: center;',
            },
            {key: 'memberId', label: '会員ID', _style: 'text-align: center; '},
            {
              key: 'last_login_datetime',
              label: '最終ログイン日時',
              _style: 'text-align: center;',
            },
            {
              key: 'status',
              label: 'ステータス\nステータス設定日',
              _style: 'text-align: center; white-space: break-spaces;',
            },
            {
              key: 'btn_member_edit',
              label: '編集',
              _style: 'text-align: center;',
            },
            {
              key: 'btn_edit',
              label: 'ステータス設定',
              _style: 'text-align: center;',
            },
          ];
        },
      },
      caption: {
        type: String,
        default: 'memberTable',
      },
      loading: Boolean,
      activePage: Number,
      itemsPerPage: Number,
      pages: Number,
      itemsSorter: Object,
    },
    data() {
      return {
        btn_clicked: false,
      };
    },
    methods: {
      isChangableStatus(val1, val2, member_no) {
        let ret = true;
        switch (val1) {
          case 0: // 未対応
            // 承認、非承認に変更できる
            if (val2 === 1 || val2 === 2) {
              ret = true;
            } else {
              ret = false;
            }
            break;
          case 2: // 非承認
            if (!member_no) {
              if (val2 === 1) {
                ret = true;
              } else {
                ret = false;
              }
            }
            break;
          case 1: // 承認
          case 8: // 一時停止
          case 9: // 退会
            if (val1 === val2) {
              ret = false;
            } else {
              ret = true;
            }
            break;
          default:
            ret = false;
        }
        return ret;
      },
      addNewMember() {
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({path: 'members/new'});
      },
      editMember(item) {
        console.log(`edit member: ${JSON.stringify(item)}`);
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({path: `members/${item.member_request_no}/edit`});
      },
      changeStatus(item, status) {
        this.$emit('changeStatus', {
          item,
          status,
        });
      },
      pageChange(val) {
        if (this.items.length > 0) {
          this.$emit('page-change', val);
        }
      },
      viewStatusHistory(item) {
        this.$emit('onViewStatusHistory', item);
      },
      sorterChange(val) {
        this.$emit('sorter-change', val);
      },
      paginationChange(val) {
        this.$emit('pagination-change', val);
      },
    },
  };
</script>
<style type="text/scss">
  .btn-status-row {
    width: 100%;
    margin: 0.2rem 0;
  }
  .btn-status-col {
    width: 50%;
    display: inline-block;
    padding: 0.25rem;
  }
  .btn-status {
    width: 100%;
    padding: 0.2rem;
  }
  .btn-history-col {
    width: 100%;
    display: inline-block;
    padding: 0.25rem;
  }
  .btn-history {
    width: 100%;
    padding: 0.25rem;
  }
  .link-text {
    cursor: pointer;
    text-decoration: underline;
  }
</style>
