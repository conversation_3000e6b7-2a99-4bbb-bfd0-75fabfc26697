const getRandomInt = (min, max) =>
  Math.floor(Math.random() * (max - min + 1)) + min

const generateUniqueId = () => getRandomInt(10, 9999)

// Categorized product data for consistent generation
const productCategories = {
  fashion: {
    brands: [
      {jp: 'ルイヴィトン', en: 'LOUIS VUITTON'},
      {jp: 'グッチ', en: 'GUCCI'},
      {jp: 'エルメス', en: 'HERMÈS'},
      {jp: 'シャネル', en: 'CHANEL'},
    ],
    productTypes: [
      {jp: 'ショルダー バッグ', en: 'Shoulder Bag'},
      {jp: 'トートバッグ', en: 'Tote Bag'},
      {jp: 'チェーンウォレット', en: 'Chain Wallet'},
      {jp: 'クロスボディ バッグ', en: 'Crossbody Bag'},
      {jp: 'ハンドバッグ', en: 'Handbag'},
      {jp: 'ウォレット', en: 'Wallet'},
    ],
    adjectives: [
      {jp: 'ダミエ アズール', en: 'Damier Azur'},
      {jp: 'モノグラム キャンバス', en: 'Monogram Canvas'},
      {jp: 'マトラッセ', en: 'Matelassé'},
      {jp: 'スモール レザーグッズ', en: 'Small Leather Goods'},
      {jp: 'エピ レザー', en: 'Epi Leather'},
      {jp: 'ヴェルニ', en: 'Vernis'},
      {jp: 'ブラック レザー', en: 'Black Leather'},
      {jp: 'レッド スウェード', en: 'Red Suede'},
      {jp: 'キルティング', en: 'Quilting'},
      {jp: 'エナメル', en: 'Enamel'},
    ]
  },
  watches: {
    brands: [
      {jp: 'ロレックス', en: 'ROLEX'},
      {jp: 'オメガ', en: 'OMEGA'},
      {jp: 'カルティエ', en: 'CARTIER'},
      {jp: 'タグホイヤー', en: 'TAG HEUER'},
    ],
    productTypes: [
      {jp: '腕時計', en: 'Wristwatch'},
      {jp: 'クロノグラフ 時計', en: 'Chronograph Watch'},
      {jp: 'ダイバーズウォッチ', en: 'Diver\'s Watch'},
      {jp: '自動巻き時計', en: 'Automatic Watch'},
      {jp: 'クォーツ時計', en: 'Quartz Watch'},
      {jp: 'スポーツウォッチ', en: 'Sports Watch'},
    ],
    adjectives: [
      {jp: 'シルバー フレーム', en: 'Silver Frame'},
      {jp: 'ゴールド仕上げ', en: 'Gold Finish'},
      {jp: 'ステンレススチール', en: 'Stainless Steel'},
      {jp: 'セラミック ベゼル', en: 'Ceramic Bezel'},
      {jp: 'サファイアクリスタル', en: 'Sapphire Crystal'},
      {jp: 'チタン製', en: 'Titanium'},
      {jp: 'ローズゴールド', en: 'Rose Gold'},
      {jp: 'プラチナ', en: 'Platinum'},
    ]
  },
  // automotive: {
  //   brands: [
  //     {jp: 'ベンツ', en: 'Mercedes-Benz'},
  //     {jp: 'BMW', en: 'BMW'},
  //     {jp: 'アウディ', en: 'AUDI'},
  //     {jp: 'ポルシェ', en: 'PORSCHE'},
  //   ],
  //   productTypes: [
  //     {jp: 'セダン 車', en: 'Sedan'},
  //     {jp: 'スポーツカー', en: 'Sports Car'},
  //     {jp: 'SUV 車', en: 'SUV'},
  //     {jp: 'ハッチバック', en: 'Hatchback'},
  //     {jp: 'クーペ', en: 'Coupe'},
  //     {jp: 'コンバーチブル', en: 'Convertible'},
  //   ],
  //   adjectives: [
  //     {jp: 'アイボリー ホワイト', en: 'Ivory White'},
  //     {jp: 'ブルー メタリック', en: 'Blue Metallic'},
  //     {jp: 'マットブラック', en: 'Matte Black'},
  //     {jp: 'パール ホワイト', en: 'Pearl White'},
  //     {jp: 'カーボンファイバー', en: 'Carbon Fiber'},
  //     {jp: 'AMG パッケージ', en: 'AMG Package'},
  //     {jp: 'Mスポーツ', en: 'M Sports'},
  //     {jp: 'ターボチャージャー', en: 'Turbocharger'},
  //   ]
  // }
}

const statusTags = [
  {jp: '♪', en: '♪'},
  {jp: '正規品', en: 'Authentic'},
  {jp: '新品同様', en: 'Like New'},
  {jp: '限定モデル', en: 'Limited Edition'},
  {jp: '高級仕様', en: 'Luxury'},
  {jp: '希少品', en: 'Rare'},
  {jp: '国内正規', en: 'Domestic Genuine'},
  {jp: '美品', en: 'Beautiful Item'},
]

// Japanese text blocks for description generation
const japaneseTextBlocks = [
  'この商品は高品質な素材を使用しており、長年にわたってご愛用いただけます。',
  '職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます。',
  '優雅なデザインと実用性を兼ね備えた、まさに理想的なアイテムです。',
  '上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。',
  '伝統的な技法と現代的なセンスが融合した、特別な一品です。',
  '厳選された素材のみを使用し、熟練の技術者が手がけた傑作です。',
  'エレガントな外観と機能美を追求した、プレミアムグレードの商品です。',
  '細やかな装飾と精巧な作りが魅力的な、コレクターアイテムです。',
  '時代を超えて愛され続ける普遍的なデザインを採用しています。',
  '最高級の品質基準をクリアした、信頼性の高い製品です。'
]

// English text blocks for description generation
const englishTextBlocks = [
  'This product is crafted with premium materials, ensuring long-lasting durability and elegance.',
  'Meticulously handcrafted by skilled artisans, every detail reflects exceptional quality and precision.',
  'A perfect fusion of sophisticated design and practical functionality for discerning customers.',
  'Features exquisite finishing and refined styling that exemplifies luxury and attention to detail.',
  'Represents a harmonious blend of traditional craftsmanship and contemporary aesthetic sensibilities.',
  'Created exclusively with carefully selected materials by master craftsmen using time-honored techniques.',
  'Embodies elegance and functional beauty, meeting the highest standards of premium-grade excellence.',
  'An extraordinary collector\'s item showcasing intricate decorative elements and superior construction.',
  'Timeless design that transcends trends, ensuring enduring appeal for generations to come.',
  'Exceeds the most stringent quality standards, delivering unparalleled reliability and performance.'
]

// Chinese text blocks for description generation
const chineseTextBlocks = [
  '这款产品采用优质材料制造，确保长期使用的耐用性和优雅性。',
  '由技艺精湛的工匠精心手工制作，每个细节都体现出卓越的品质和精准度。',
  '融合了精致设计和实用功能的完美结合，专为挑剔的客户打造。',
  '具有精美的表面处理和精致的造型，体现了奢华和对细节的关注。',
  '代表了传统工艺与现代美学的和谐融合。',
  '专门使用精心挑选的材料，由掌握传统技艺的大师工匠制作。',
  '体现了优雅和功能美，达到了顶级品质的最高标准。',
  '展示精致装饰元素和卓越工艺的非凡收藏品。',
  '超越潮流的永恒设计，确保世代传承的持久魅力。',
  '超越最严格的质量标准，提供无与伦比的可靠性和性能。'
]

// Function to generate random description text
const generateRandomDescription = (language, targetLength = 1000) => {
  let textBlocks
  let connector
  
  switch (language) {
    case 'zh':
      textBlocks = chineseTextBlocks
      connector = '此外，'
      break
    case 'ja':
      textBlocks = japaneseTextBlocks
      connector = 'また、'
      break
    default:
      textBlocks = englishTextBlocks
      connector = ' Furthermore, '
  }
  
  let description = ''

  while (description.length < targetLength) {
    const randomBlock = textBlocks[getRandomInt(0, textBlocks.length - 1)]
    description += randomBlock

    if (description.length < targetLength) {
      description += connector
    }
  }

  // Trim to exact length
  return description.substring(0, targetLength)
}

function getRandomCurrency(min, max, step = 1000) {
  const range = Math.floor((max - min) / step);
  const randomStep = Math.floor(Math.random() * (range + 1));
  return min + (randomStep * step);
}


// Dynamic field data sets for enhanced fake data generation
const dynamicFieldDataSets = {
  models: {
    electronics: [
      {jp: 'X-1000', en: 'X-1000', zh: 'X-1000'},
      {jp: 'Pro-Max-2024', en: 'Pro-Max-2024', zh: 'Pro-Max-2024'},
      {jp: 'Elite-500-Special', en: 'Elite-500-Special', zh: 'Elite-500-Special'},
      {jp: 'Ultra-Premium', en: 'Ultra-Premium', zh: 'Ultra-Premium'},
      {jp: 'Classic-Edition', en: 'Classic-Edition', zh: 'Classic-Edition'},
      {jp: 'Advanced-Series', en: 'Advanced-Series', zh: 'Advanced-Series'},
    ],
    fashion: [
      {jp: 'M12345-Limited', en: 'M12345-Limited', zh: 'M12345-Limited'},
      {jp: 'Signature-Collection', en: 'Signature-Collection', zh: '签名系列'},
      {jp: 'Vintage-2024', en: 'Vintage-2024', zh: '复古-2024'},
      {jp: 'Exclusive-Design', en: 'Exclusive-Design', zh: '独家设计'},
      {jp: 'Heritage-Line', en: 'Heritage-Line', zh: '传统系列'},
    ]
  },
  conditions: [
    {jp: 'A', en: 'A', zh: 'A'},
    {jp: 'B', en: 'B', zh: 'B'},
    {jp: 'C', en: 'C', zh: 'C'},
  ],
  detailedConditions: [
    {jp: '新品同様', en: 'Like New', zh: '全新状态'},
    {jp: '非常に良い', en: 'Very Good', zh: '非常好'},
    {jp: '良い', en: 'Good', zh: '良好'},
    {jp: '使用感あり', en: 'Used', zh: '有使用痕迹'},
  ],
  colors: [
    {jp: '黒', en: 'Black', zh: '黑色'},
    {jp: '白', en: 'White', zh: '白色'},
    {jp: '赤', en: 'Red', zh: '红色'},
    {jp: '青', en: 'Blue', zh: '蓝色'},
    {jp: '銀', en: 'Silver', zh: '银色'},
    {jp: '金', en: 'Gold', zh: '金色'},
    {jp: 'グレー', en: 'Gray', zh: '灰色'},
    {jp: 'ネイビー', en: 'Navy', zh: '海军蓝'},
  ],
  capacities: [
    {jp: '64GB', en: '64GB', zh: '64GB'},
    {jp: '128GB', en: '128GB', zh: '128GB'},
    {jp: '256GB', en: '256GB', zh: '256GB'},
    {jp: '512GB', en: '512GB', zh: '512GB'},
    {jp: '1TB', en: '1TB', zh: '1TB'},
  ],
  shippingMethods: [
    {jp: '宅急便', en: 'Express Delivery', zh: '快递'},
    {jp: '普通郵便', en: 'Standard Mail', zh: '标准邮件'},
    {jp: '速達', en: 'Priority Mail', zh: '加急邮件'},
    {jp: '着払い', en: 'Cash on Delivery', zh: '货到付款'},
    {jp: 'ネコポス', en: 'Compact Delivery', zh: '紧凑型快递'},
    {jp: 'レターパック', en: 'Letter Pack', zh: '信件包'},
  ],
  shippingFees: [
    {jp: '無料', en: 'Free', zh: '免费'},
    {jp: '300円', en: '300 Yen', zh: '300日元'},
    {jp: '500円', en: '500 Yen', zh: '500日元'},
    {jp: '800円', en: '800 Yen', zh: '800日元'},
    {jp: '1200円', en: '1200 Yen', zh: '1200日元'},
    {jp: '1500円', en: '1500 Yen', zh: '1500日元'},
  ],
  productTags: [
    {jp: '新品同様', en: 'Like New', zh: '全新状态'},
    {jp: '限定品', en: 'Limited Edition', zh: '限量版'},
    {jp: '希少品', en: 'Rare Item', zh: '稀有物品'},
    {jp: '正規品', en: 'Authentic', zh: '正品'},
    {jp: '美品', en: 'Excellent Condition', zh: '极佳状态'},
    {jp: 'コレクター品', en: 'Collector Item', zh: '收藏品'},
    {jp: 'ヴィンテージ', en: 'Vintage', zh: '复古'},
  ],
  categories: [
    {jp: 'エレクトロニクス', en: 'Electronics', zh: '电子产品'},
    {jp: 'ファッション', en: 'Fashion', zh: '时尚'},
    {jp: '腕時計', en: 'Watches', zh: '手表'},
    {jp: 'バッグ', en: 'Bags', zh: '包包'},
    {jp: 'アクセサリー', en: 'Accessories', zh: '配饰'},
    {jp: 'スポーツ用品', en: 'Sports Equipment', zh: '体育用品'},
  ],
  makers: [
    {jp: 'Apple', en: 'Apple', zh: 'Apple'},
    {jp: 'Samsung', en: 'Samsung', zh: 'Samsung'},
    {jp: 'Sony', en: 'Sony', zh: 'Sony'},
    {jp: 'ルイヴィトン', en: 'Louis Vuitton', zh: '路易威登'},
    {jp: 'エルメス', en: 'Hermès', zh: '爱马仕'},
    {jp: 'シャネル', en: 'Chanel', zh: '香奈儿'},
    {jp: 'グッチ', en: 'Gucci', zh: '古驰'},
  ],
  notes: [
    {jp: '付属品完備', en: 'Complete with accessories', zh: '配件齐全'},
    {jp: '箱・保証書付き', en: 'Box and warranty included', zh: '包装盒和保修卡'},
    {jp: '使用感少なく美品', en: 'Minimal wear, excellent condition', zh: '使用痕迹少，状态极佳'},
    {jp: '動作確認済み', en: 'Function tested', zh: '功能已测试'},
    {jp: 'クリーニング済み', en: 'Professionally cleaned', zh: '专业清洁'},
  ],
  sizes: [
    {jp: 'XS', en: 'XS', zh: 'XS'},
    {jp: 'S', en: 'S', zh: 'S'},
    {jp: 'M', en: 'M', zh: 'M'},
    {jp: 'L', en: 'L', zh: 'L'},
    {jp: 'XL', en: 'XL', zh: 'XL'},
    {jp: 'XXL', en: 'XXL', zh: 'XXL'},
  ]
}

// Helper function to get random translated value
function getRandomTranslatedValue(dataArray, language = 'jp') {
  const randomItem = dataArray[getRandomInt(0, dataArray.length - 1)]
  return randomItem[language] || randomItem.jp || randomItem
}

// Enhanced function to generate fake data for dynamic fields with multi-language support
function generateDynamicFieldData(field, language = 'jp') {
  const physicalName = field.physical_name?.toLowerCase() || '';
  const fieldName = field.field_name?.toLowerCase() || '';
  const inputType = field.input_type || field.type || 'text';
  const options = field.options || [];
  
  // Handle pulldown fields with provided options
  if (inputType === 'pulldown' && options.length > 0) {
    const randomOption = options[getRandomInt(0, options.length - 1)]
    return randomOption.value || randomOption.label
  }
  
  // Model/Product Number fields
  if (physicalName.includes('model') || physicalName.includes('product_number') || 
      physicalName.includes('型番') || fieldName.includes('model')) {
    const category = physicalName.includes('fashion') || fieldName.includes('fashion') ? 'fashion' : 'electronics';
    const models = dynamicFieldDataSets.models[category];
    return getRandomTranslatedValue(models, language);
  }
  
  // Condition fields (A/B/C ranks)
  if (physicalName.includes('condition') || physicalName.includes('rank') || 
      physicalName.includes('状態') || fieldName.includes('condition') ||
      physicalName.includes('grade')) {
    // For detailed condition descriptions
    if (physicalName.includes('detail') || fieldName.includes('detail')) {
      return getRandomTranslatedValue(dynamicFieldDataSets.detailedConditions, language);
    }
    // For simple rank letters (A/B/C)
    return getRandomTranslatedValue(dynamicFieldDataSets.conditions, language);
  }
  
  // Color fields
  if (physicalName.includes('color') || physicalName.includes('colour') || 
      physicalName.includes('色') || fieldName.includes('color')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.colors, language);
  }
  
  // Capacity/Storage fields
  if (physicalName.includes('capacity') || physicalName.includes('storage') || 
      physicalName.includes('size') || physicalName.includes('gb') ||
      physicalName.includes('容量') || fieldName.includes('capacity')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.capacities, language);
  }
  
  // Shipping Method fields
  if (physicalName.includes('shipping_method') || physicalName.includes('delivery') || 
      physicalName.includes('配送方法') || fieldName.includes('shipping') && !fieldName.includes('fee')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.shippingMethods, language);
  }
  
  // Shipping Fee fields
  if (physicalName.includes('shipping_fee') || physicalName.includes('delivery_fee') || 
      physicalName.includes('配送料') || physicalName.includes('送料') || physicalName.includes('postage') ||
      (fieldName.includes('shipping') && fieldName.includes('fee'))) {
    return getRandomTranslatedValue(dynamicFieldDataSets.shippingFees, language);
  }
  
  // Product Tag fields
  if (physicalName.includes('tag') || physicalName.includes('label') || 
      physicalName.includes('タグ') || fieldName.includes('tag')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.productTags, language);
  }
  
  // Category fields
  if (physicalName.includes('category') || physicalName.includes('カテゴリ') || 
      fieldName.includes('category')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.categories, language);
  }
  
  // Maker/Brand fields
  if (physicalName.includes('maker') || physicalName.includes('brand') || 
      physicalName.includes('manufacturer') || physicalName.includes('メーカー') ||
      fieldName.includes('maker') || fieldName.includes('brand')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.makers, language);
  }
  
  // Size fields
  if (physicalName.includes('size') && !physicalName.includes('capacity') || 
      physicalName.includes('サイズ') || fieldName.includes('size')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.sizes, language);
  }
  
  // Notes/Remarks fields
  if (physicalName.includes('note') || physicalName.includes('remark') || 
      physicalName.includes('comment') || physicalName.includes('備考') ||
      fieldName.includes('note') || fieldName.includes('remark')) {
    return getRandomTranslatedValue(dynamicFieldDataSets.notes, language);
  }
  
  // Generic text fields - generate contextual content
  if (inputType === 'text' || inputType === 'textarea') {
    const textOptions = [
      {jp: '高品質な商品です', en: 'High quality item', zh: '高品质商品'},
      {jp: '状態良好', en: 'Good condition', zh: '状态良好'},
      {jp: '正規品保証', en: 'Authentic guarantee', zh: '正品保证'},
      {jp: '即日発送可能', en: 'Same day shipping available', zh: '当日发货'},
      {jp: '付属品完備', en: 'Complete with accessories', zh: '配件齐全'}
    ];
    return getRandomTranslatedValue(textOptions, language);
  }
  
  // For checkbox fields, return random selection
  if (inputType === 'checkbox' && options.length > 0) {
    const selectedOptions = []
    const numSelections = getRandomInt(1, Math.min(3, options.length))
    
    for (let i = 0; i < numSelections; i++) {
      const randomOption = options[getRandomInt(0, options.length - 1)]
      if (!selectedOptions.includes(randomOption.value)) {
        selectedOptions.push(randomOption.value)
      }
    }
    return selectedOptions
  }
  
  // Default fallback
  return null
}
}

export const createFakeData = (exhibition_no, num, auctionType) => {
  return Array.from({length: num}, () => {
    const randomId = generateUniqueId()

    // Select a random category first
    const categoryNames = Object.keys(productCategories)
    const selectedCategoryName = categoryNames[getRandomInt(0, categoryNames.length - 1)]
    const selectedCategory = productCategories[selectedCategoryName]

    // Select elements from the same category to ensure consistency
    const brand = selectedCategory.brands[getRandomInt(0, selectedCategory.brands.length - 1)]
    const adjective = selectedCategory.adjectives[getRandomInt(0, selectedCategory.adjectives.length - 1)]
    const productType = selectedCategory.productTypes[getRandomInt(0, selectedCategory.productTypes.length - 1)]
    const tag1 = statusTags[getRandomInt(0, statusTags.length - 1)]
    const tag2 = statusTags[getRandomInt(0, statusTags.length - 1)]

    // Create a more natural product name structure
    const productNameJP = `${brand.jp} ${adjective.jp} ${productType.jp} ${tag1.jp} ${tag2.jp}`
    const productNameEN = `${brand.en} ${adjective.en} ${productType.en} ${tag1.en} ${tag2.en}`
    const productNameZH = `${brand.jp} ${adjective.jp} ${productType.jp} ${tag1.jp} ${tag2.jp}` // For demo, using JP names for Chinese

    return {
      validate: false,
      data: {
        exhibition_no: exhibition_no,
        "lot_no": null,
        manage_no: `ID000${randomId}`,
        quantity: 1,
        lowest_bid_price: getRandomCurrency(12000, 40000),
        lowest_bid_accept_price: auctionType === 1 ? getRandomCurrency(40000, 55000) : getRandomCurrency(1000, 5000),
        lowest_bid_quantity: 1,
        lowest_bid_accept_quantity: auctionType === 1 ? 1 : 20,
        recommend_flag: randomId % 2 === 0 ? 1 : 0,
        localized_json_array: [
          {
            language_code: 'en',
            field_map: {
              product_name: productNameEN,
              description: generateRandomDescription('en', 1000),
            },
          },
          {
            language_code: 'ja',
            field_map: {
              product_name: productNameJP,
              description: generateRandomDescription('ja', 1000),
            },
          },
          {
            language_code: 'zh',
            field_map: {
              product_name: productNameZH,
              description: generateRandomDescription('zh', 1000),
            },
          },
        ],
        picturePath: [],
      },
    }
  })
}

// Export the dynamic field generator for use in components
export { generateDynamicFieldData }
