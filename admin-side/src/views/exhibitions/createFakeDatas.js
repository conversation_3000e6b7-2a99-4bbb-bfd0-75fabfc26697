const getRandomInt = (min, max) =>
  Math.floor(Math.random() * (max - min + 1)) + min

const generateUniqueId = () => getRandomInt(10, 9999)

// Categorized product data for consistent generation
const productCategories = {
  fashion: {
    brands: [
      {jp: 'ルイヴィトン', en: 'LOUIS VUITTON'},
      {jp: 'グッチ', en: 'GUCCI'},
      {jp: 'エルメス', en: 'HERMÈS'},
      {jp: 'シャネル', en: 'CHANEL'},
    ],
    productTypes: [
      {jp: 'ショルダー バッグ', en: 'Shoulder Bag'},
      {jp: 'トートバッグ', en: 'Tote Bag'},
      {jp: 'チェーンウォレット', en: 'Chain Wallet'},
      {jp: 'クロスボディ バッグ', en: 'Crossbody Bag'},
      {jp: 'ハンドバッグ', en: 'Handbag'},
      {jp: 'ウォレット', en: 'Wallet'},
    ],
    adjectives: [
      {jp: 'ダミエ アズール', en: 'Damier Azur'},
      {jp: 'モノグラム キャンバス', en: 'Monogram Canvas'},
      {jp: 'マトラッセ', en: 'Matelassé'},
      {jp: 'スモール レザーグッズ', en: 'Small Leather Goods'},
      {jp: 'エピ レザー', en: 'Epi Leather'},
      {jp: 'ヴェルニ', en: 'Vernis'},
      {jp: 'ブラック レザー', en: 'Black Leather'},
      {jp: 'レッド スウェード', en: 'Red Suede'},
      {jp: 'キルティング', en: 'Quilting'},
      {jp: 'エナメル', en: 'Enamel'},
    ]
  },
  watches: {
    brands: [
      {jp: 'ロレックス', en: 'ROLEX'},
      {jp: 'オメガ', en: 'OMEGA'},
      {jp: 'カルティエ', en: 'CARTIER'},
      {jp: 'タグホイヤー', en: 'TAG HEUER'},
    ],
    productTypes: [
      {jp: '腕時計', en: 'Wristwatch'},
      {jp: 'クロノグラフ 時計', en: 'Chronograph Watch'},
      {jp: 'ダイバーズウォッチ', en: 'Diver\'s Watch'},
      {jp: '自動巻き時計', en: 'Automatic Watch'},
      {jp: 'クォーツ時計', en: 'Quartz Watch'},
      {jp: 'スポーツウォッチ', en: 'Sports Watch'},
    ],
    adjectives: [
      {jp: 'シルバー フレーム', en: 'Silver Frame'},
      {jp: 'ゴールド仕上げ', en: 'Gold Finish'},
      {jp: 'ステンレススチール', en: 'Stainless Steel'},
      {jp: 'セラミック ベゼル', en: 'Ceramic Bezel'},
      {jp: 'サファイアクリスタル', en: 'Sapphire Crystal'},
      {jp: 'チタン製', en: 'Titanium'},
      {jp: 'ローズゴールド', en: 'Rose Gold'},
      {jp: 'プラチナ', en: 'Platinum'},
    ]
  },
  // automotive: {
  //   brands: [
  //     {jp: 'ベンツ', en: 'Mercedes-Benz'},
  //     {jp: 'BMW', en: 'BMW'},
  //     {jp: 'アウディ', en: 'AUDI'},
  //     {jp: 'ポルシェ', en: 'PORSCHE'},
  //   ],
  //   productTypes: [
  //     {jp: 'セダン 車', en: 'Sedan'},
  //     {jp: 'スポーツカー', en: 'Sports Car'},
  //     {jp: 'SUV 車', en: 'SUV'},
  //     {jp: 'ハッチバック', en: 'Hatchback'},
  //     {jp: 'クーペ', en: 'Coupe'},
  //     {jp: 'コンバーチブル', en: 'Convertible'},
  //   ],
  //   adjectives: [
  //     {jp: 'アイボリー ホワイト', en: 'Ivory White'},
  //     {jp: 'ブルー メタリック', en: 'Blue Metallic'},
  //     {jp: 'マットブラック', en: 'Matte Black'},
  //     {jp: 'パール ホワイト', en: 'Pearl White'},
  //     {jp: 'カーボンファイバー', en: 'Carbon Fiber'},
  //     {jp: 'AMG パッケージ', en: 'AMG Package'},
  //     {jp: 'Mスポーツ', en: 'M Sports'},
  //     {jp: 'ターボチャージャー', en: 'Turbocharger'},
  //   ]
  // }
}

const statusTags = [
  {jp: '♪', en: '♪'},
  {jp: '正規品', en: 'Authentic'},
  {jp: '新品同様', en: 'Like New'},
  {jp: '限定モデル', en: 'Limited Edition'},
  {jp: '高級仕様', en: 'Luxury'},
  {jp: '希少品', en: 'Rare'},
  {jp: '国内正規', en: 'Domestic Genuine'},
  {jp: '美品', en: 'Beautiful Item'},
]

// Japanese text blocks for description generation
const japaneseTextBlocks = [
  'この商品は高品質な素材を使用しており、長年にわたってご愛用いただけます。',
  '職人によって丁寧に作られた逸品で、細部にまでこだわりが感じられます。',
  '優雅なデザインと実用性を兼ね備えた、まさに理想的なアイテムです。',
  '上質な仕上がりと洗練されたスタイルが特徴的な製品となっております。',
  '伝統的な技法と現代的なセンスが融合した、特別な一品です。',
  '厳選された素材のみを使用し、熟練の技術者が手がけた傑作です。',
  'エレガントな外観と機能美を追求した、プレミアムグレードの商品です。',
  '細やかな装飾と精巧な作りが魅力的な、コレクターアイテムです。',
  '時代を超えて愛され続ける普遍的なデザインを採用しています。',
  '最高級の品質基準をクリアした、信頼性の高い製品です。'
]

// English text blocks for description generation
const englishTextBlocks = [
  'This product is crafted with premium materials, ensuring long-lasting durability and elegance.',
  'Meticulously handcrafted by skilled artisans, every detail reflects exceptional quality and precision.',
  'A perfect fusion of sophisticated design and practical functionality for discerning customers.',
  'Features exquisite finishing and refined styling that exemplifies luxury and attention to detail.',
  'Represents a harmonious blend of traditional craftsmanship and contemporary aesthetic sensibilities.',
  'Created exclusively with carefully selected materials by master craftsmen using time-honored techniques.',
  'Embodies elegance and functional beauty, meeting the highest standards of premium-grade excellence.',
  'An extraordinary collector\'s item showcasing intricate decorative elements and superior construction.',
  'Timeless design that transcends trends, ensuring enduring appeal for generations to come.',
  'Exceeds the most stringent quality standards, delivering unparalleled reliability and performance.'
]

// Function to generate random description text
const generateRandomDescription = (language, targetLength = 1000) => {
  const textBlocks = language === 'ja' ? japaneseTextBlocks : englishTextBlocks
  let description = ''

  while (description.length < targetLength) {
    const randomBlock = textBlocks[getRandomInt(0, textBlocks.length - 1)]
    description += randomBlock

    if (description.length < targetLength) {
      description += language === 'ja' ? 'また、' : ' Furthermore, '
    }
  }

  // Trim to exact length
  return description.substring(0, targetLength)
}

function getRandomCurrency(min, max, step = 1000) {
  const range = Math.floor((max - min) / step);
  const randomStep = Math.floor(Math.random() * (range + 1));
  return min + (randomStep * step);
}


export const createFakeData = (exhibition_no, num, auctionType) => {
  return Array.from({length: num}, () => {
    const randomId = generateUniqueId()

    // Select a random category first
    const categoryNames = Object.keys(productCategories)
    const selectedCategoryName = categoryNames[getRandomInt(0, categoryNames.length - 1)]
    const selectedCategory = productCategories[selectedCategoryName]

    // Select elements from the same category to ensure consistency
    const brand = selectedCategory.brands[getRandomInt(0, selectedCategory.brands.length - 1)]
    const adjective = selectedCategory.adjectives[getRandomInt(0, selectedCategory.adjectives.length - 1)]
    const productType = selectedCategory.productTypes[getRandomInt(0, selectedCategory.productTypes.length - 1)]
    const tag1 = statusTags[getRandomInt(0, statusTags.length - 1)]
    const tag2 = statusTags[getRandomInt(0, statusTags.length - 1)]

    // Create a more natural product name structure
    const productNameJP = `${brand.jp} ${adjective.jp} ${productType.jp} ${tag1.jp} ${tag2.jp}`
    const productNameEN = `${brand.en} ${adjective.en} ${productType.en} ${tag1.en} ${tag2.en}`

    return {
      validate: false,
      data: {
        exhibition_no: exhibition_no,
        "lot_no": null,
        manage_no: `ID000${randomId}`,
        quantity: 1,
        lowest_bid_price: getRandomCurrency(12000, 40000),
        lowest_bid_accept_price: auctionType === 1 ? getRandomCurrency(40000, 55000) : getRandomCurrency(1000, 5000),
        lowest_bid_quantity: 1,
        lowest_bid_accept_quantity: auctionType === 1 ? 1 : 20,
        recommend_flag: randomId % 2 === 0 ? 1 : 0,
        localized_json_array: [
          {
            language_code: 'en',
            field_map: {
              product_name: productNameEN,
              description: generateRandomDescription('en', 1000),
            },
          },
          {
            language_code: 'ja',
            field_map: {
              product_name: productNameJP,
              description: generateRandomDescription('ja', 1000),
            },
          },
        ],
        picturePath: [],
      },
    }
  })
}
