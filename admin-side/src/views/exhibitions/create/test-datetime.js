// Test file for datetime functionality
const addDaysToDateTime = (dateTimeString, days) => {
  if (!dateTimeString) return null

  // Parse the datetime string (format: "YYYY-MM-DD HH:MM")
  const date = new Date(dateTimeString.replace(' ', 'T'))
  if (isNaN(date.getTime())) return null

  // Add the specified number of days
  date.setDate(date.getDate() + days)

  // Format back to "YYYY-MM-DD HH:MM" format
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// Test cases
console.log('Test 1:', addDaysToDateTime('2025-01-31 15:23', 7)) // Should return: 2025-02-07 15:23
console.log('Test 2:', addDaysToDateTime('2025-02-28 10:00', 7)) // Should return: 2025-03-07 10:00
console.log('Test 3:', addDaysToDateTime('2024-02-28 23:59', 7)) // Should return: 2024-03-06 23:59 (leap year)
console.log('Test 4:', addDaysToDateTime(null, 7)) // Should return: null
console.log('Test 5:', addDaysToDateTime('invalid-date', 7)) // Should return: null
