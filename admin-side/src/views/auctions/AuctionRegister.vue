<template>
  <div class="mb-3">
    <CCard>
      <CForm>
        <CCardHeader>
          <strong>入札会登録</strong>
        </CCardHeader>
        <CCardBody>
          <CCol sm="9">
            <strong>入札会名</strong>
          </CCol>
          <CRow
            v-for="(lang, key, index) in languageList"
            :key="index"
            class="mt-3"
          >
            <CCol sm="2" class="d-flex align-items-center">
              <label :class="languageList.length > 1 ? 'pl-3' : ''">{{
                lang.label
              }}</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="10">
              <CFormInput
                :name="'exhibition_name_' + lang.value"
                v-model="exhibitionData['exhibition_name_' + lang.value]"
                class="col-sm-4 mb-0 pl-0"
                maxLength="30"
              />
            </CCol>
            <CCol sm="3"></CCol>
          </CRow>
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
        >
          <strong>時間設定</strong>
        </CCardHeader>
        <CCardBody>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>下見開始日時</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                horizontal
                v-model="previewStartDate"
                :invalid="!dateValidate.previewStartDate"
                :max="maxDate"
                @change="
                  e => {
                    dateValidate.previewStartDate = e.target.validity.valid
                    changePreviewStartDateTime()
                  }
                "
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                horizontal
                v-model="previewStartTime"
                class="ml-2"
                :invalid="!dateValidate.previewStartTime"
                @change="
                  e => {
                    dateValidate.previewStartTime = e.target.validity.valid
                    changePreviewStartDateTime()
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>入札開始日時</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                horizontal
                v-model="startDate"
                :invalid="!dateValidate.startDate"
                :max="maxDate"
                @change="
                  e => {
                    dateValidate.startDate = e.target.validity.valid
                    changeStartDateTime()
                  }
                "
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                horizontal
                class="ml-2"
                v-model="startTime"
                :invalid="!dateValidate.startTime"
                @change="
                  e => {
                    dateValidate.startTime = e.target.validity.valid
                    changeStartDateTime()
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>入札終了日時</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                horizontal
                v-model="endDate"
                :invalid="!dateValidate.endDate"
                :max="maxDate"
                @change="
                  e => {
                    dateValidate.endDate = e.target.validity.valid
                    changeEndDateTime()
                  }
                "
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                horizontal
                class="ml-2"
                v-model="endTime"
                :invalid="!dateValidate.endTime"
                @change="
                  e => {
                    dateValidate.endTime = e.target.validity.valid
                    changeEndDateTime()
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>閲覧終了日時</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                horizontal
                v-model="previewEndDate"
                :invalid="!dateValidate.previewEndDate"
                :max="maxDate"
                @change="
                  e => {
                    dateValidate.previewEndDate = e.target.validity.valid
                    changePreviewEndDateTime()
                  }
                "
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                horizontal
                class="ml-2"
                v-model="previewEndTime"
                :invalid="!dateValidate.previewEndTime"
                @change="
                  e => {
                    dateValidate.previewEndTime = e.target.validity.valid
                    changePreviewEndDateTime()
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>入札数</label>
            </CCol>
            <CCol sm="auto">
              <CFormSelect
                name="showBidCountFlag"
                :options="showBidCountFlagOptions"
                v-model="exhibitionData.showBidCountFlag"
              />
            </CCol>
          </CRow>
          <CRow
            v-if="
              auctionClassificationList && auctionClassificationList.length > 1
            "
            class="mb-3"
          >
            <CCol sm="3">
              <label>方式</label>
            </CCol>
            <CCol sm="auto">
              <CFormSelect
                name="auction_classification"
                :options="auctionClassificationList"
                v-model="exhibitionData.auctionClassification"
                @change="e => onClassificationChange(e?.target?.value)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3" v-if="dispDealFlag">
            <CCol sm="3">
              <label>即決</label>
            </CCol>
            <CCol sm="auto">
              <CFormSelect
                name="dealFlag"
                :options="dealBidPriceOptions"
                v-model="exhibitionData.dealFlag"
              />
            </CCol>
          </CRow>
          <CRow v-show="false && exhibitionData.auctionClassification === '1'">
            <CCol sm="3">
              <label>入札単位区分</label>
            </CCol>
            <CCol sm="9">
              <CFormCheck
                v-for="option in pitchOptions"
                :key="option.value"
                type="radio"
                :id="`pitch_option_${option.value}`"
                name="pitch_option"
                :value="option.value"
                :label="option.label"
                v-model="exhibitionData.pitchOption"
                inline
              />
            </CCol>
          </CRow>
          <CRow
            v-if="
              String(exhibitionData.pitchOption) === '1' &&
              pitchWidthList?.length > 1
            "
          >
            <CCol sm="3">
              <label>入札単位</label>
            </CCol>
            <CCol sm="2">
              <CFormSelect
                name="pitch_width"
                :options="pitchWidthList"
                v-model:value="exhibitionData.pitchWidth"
                class="mb-0"
                addInputClasses="text-right"
              />
            </CCol>
          </CRow>

          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="mt-3"
          >
            <CCol sm="3"> 自動延長設定 </CCol>
            <CCol sm="9" class="pl-0">
              <CFormCheck
                v-for="option in flagOptions"
                :key="option.value"
                type="radio"
                :id="`extend_flag_${option.value}`"
                :value="option.value"
                :label="option.label"
                v-model="exhibitionData.extendFlag"
                inline
              />
            </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="d-flex flex-row align-items-center"
          >
            <CCol sm="3" />
            <CCol sm="auto">終了前　</CCol>
            <CCol sm="auto">
              <CInputGroup>
                <CFormInput
                  v-model="exhibitionData.extendJudgeMinutes"
                  style="width: 60px"
                  :disabled="exhibitionData.extendFlag === '0'"
                  maxLength="3"
                />
                <CInputGroupText>分</CInputGroupText>
              </CInputGroup>
            </CCol>
            <CCol sm="auto"> 以内なら延長 </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="mt-3 d-flex flex-row align-items-center"
          >
            <CCol sm="3" />
            <CCol sm="auto"> 延長時間 </CCol>
            <CCol sm="auto">
              <CInputGroup>
                <CFormInput
                  v-model="exhibitionData.extendMinutes"
                  style="width: 60px"
                  :disabled="exhibitionData.extendFlag === '0'"
                  append="分"
                  maxLength="3"
                />
                <CInputGroupText>分</CInputGroupText>
              </CInputGroup>
            </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="d-flex flex-row align-items-center mt-3"
          >
            <CCol sm="3" />
            <CCol sm="auto"> 最大延長日時 </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                horizontal
                v-model="extendDate"
                :disabled="exhibitionData.extendFlag === '0'"
                :invalid="!dateValidate.extendDate"
                :max="maxDate"
                @change="
                  e => {
                    dateValidate.extendDate = e.target.validity.valid
                    changeMaxExtendDateTime()
                  }
                "
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                horizontal
                v-model="extendTime"
                :disabled="exhibitionData.extendFlag === '0'"
                :invalid="!dateValidate.extendTime"
                @change="
                  e => {
                    dateValidate.extendTime = e.target.validity.valid
                    changeMaxExtendDateTime()
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="mt-3"
          >
            <CCol sm="3"> もう一息表示 </CCol>
            <CCol sm="9" class="pl-0">
              <CFormCheck
                v-for="option in flagOptions"
                :key="option.value"
                type="radio"
                :id="`little_more_display_flag_${option.value}`"
                :value="option.value"
                :label="option.label"
                v-model="exhibitionData.littleMoreDisplayFlag"
                inline
              />
            </CCol>
            <CCol sm="3" />
            <CCol sm="9" class="d-flex flex-row align-items-center gap-2">
              最低落札価格まで
              <CFormInput
                name="more_little_judge_pitch"
                v-model="exhibitionData.moreLittleJudgePitch"
                style="display: inline-block; width: 70px"
                :disabled="exhibitionData.littleMoreDisplayFlag === '0'"
                maxLength="3"
              />
              入札単位以内の場合は「もう一息」表示する。
            </CCol>
          </CRow>
          <CElementCover v-if="loading" :opacity="0.8" />
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
          v-if="
            exhibitionData.auctionClassification === '1'
              ? bidOption?.sankaseigen_seri == '1'
              : bidOption?.sankaseigen_fuin == '1'
          "
        >
          <strong>参加制限設定</strong>
        </CCardHeader>
        <CCardBody
          v-if="
            exhibitionData.auctionClassification === '1'
              ? bidOption?.sankaseigen_seri == '1'
              : bidOption?.sankaseigen_fuin == '1'
          "
        >
          <CRow class="mb-3">
            <CCol sm="3">
              <CFormSelect
                name="join_object"
                :options="bidMemberOptions"
                v-model="exhibitionData.joinObject"
              />
            </CCol>
            <CCol sm="3" v-if="exhibitionData.joinObject === '2'">
              <MemberSelectButton @confirm="selectedMembers = $event" :allMembers="allMembers"/>
            </CCol>
          </CRow>
          <CRow v-if="exhibitionData.joinObject === '2'">
            <CCol sm="12">
              <div class="gap-1">
                <CBadge
                  v-for="member in selectedMembers.slice(0, moreShowMember ? selectedMembers.length : 10)"
                  :key="member.member_id"
                  textBgColor="light"
                  shape="rounded-pill"
                >
                  <CIcon icon="cil-user" />{{ member.member_id }}
                </CBadge>
              </div>
              <div v-if="!moreShowMember && selectedMembers.length > 10" class="mt-2">
                <a style="cursor: pointer;" @click="moreShowMember = true" class="text-link">もっと見る</a>
              </div>
            </CCol>
          </CRow>
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
        >
          <strong>入札仕様</strong>
        </CCardHeader>
        <CCardBody>
          <CRow class="mb-3">
            <CCol sm="3">参加制限設定</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.sankaseigen_seri == '1'
                  ? 'あり'
                  : bidOption?.sankaseigen_seri == '0'
                    ? 'なし'
                    : '-'
                : bidOption?.sankaseigen_fuin == '1'
                  ? 'あり'
                  : bidOption?.sankaseigen_fuin == '0'
                    ? 'なし'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">最低落札価格</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.minimum_price_seri == '1'
                  ? 'あり'
                  : bidOption?.minimum_price_seri == '0'
                    ? 'なし'
                    : '-'
                : bidOption?.minimum_price_fuin == '1'
                  ? 'あり'
                  : bidOption?.minimum_price_fuin == '0'
                    ? 'なし'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">入札制限</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.bid_limit_seri == '1'
                  ? '現在価格より高い'
                  : bidOption?.bid_limit_seri == '2'
                    ? '自由'
                    : '-'
                : bidOption?.bid_limit_fuin == '1'
                  ? '現在価格より高い'
                  : bidOption?.bid_limit_fuin == '2'
                    ? '自由'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">即決</CCol>
            <CCol sm="9">{{ getDealOption }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">入札取消</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.bid_cancel_seri == '1'
                  ? 'あり'
                  : bidOption?.bid_cancel_seri == '0'
                    ? 'なし'
                    : '-'
                : bidOption?.bid_cancel_fuin == '1'
                  ? 'あり'
                  : bidOption?.bid_cancel_fuin == '0'
                    ? 'なし'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">延長</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.extension_seri == '1'
                  ? 'あり'
                  : bidOption?.extension_seri == '0'
                    ? 'なし'
                    : '-'
                : bidOption?.extension_fuin == '1'
                  ? 'あり'
                  : bidOption?.extension_fuin == '0'
                    ? 'なし'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">落札価格</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.bid_success_price_seri == '1'
                  ? 'ファーストプライス'
                  : bidOption?.bid_success_price_seri == '2'
                    ? 'セカンドプライス'
                    : '-'
                : bidOption?.bid_success_price_fuin == '1'
                  ? 'ファーストプライス'
                  : bidOption?.bid_success_price_fuin == '2'
                    ? 'セカンドプライス'
                    : '-'
            }}</CCol>
          </CRow>
          <CRow class="">
            <CCol sm="3">落札者決定</CCol>
            <CCol sm="9">{{
              exhibitionData.auctionClassification === '1'
                ? bidOption?.winner_select_seri == '1'
                  ? '自動'
                  : bidOption?.winner_select_seri == '2'
                    ? '手動'
                    : '-'
                : bidOption?.winner_select_fuin == '1'
                  ? '自動'
                  : bidOption?.winner_select_fuin == '2'
                    ? '手動'
                    : '-'
            }}</CCol>
          </CRow>
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
        >
          <strong>一括出展</strong>
        </CCardHeader>
        <CCardBody>
          <div v-if="languageListLoading" class="text-center mt-3">
            <CSpinner />
          </div>
          <div v-else>
            <CRow
              v-for="lang in languageList"
              :key="lang.value"
              class="align-items-center mb-3"
            >
              <CCol md="2">{{ lang.label }}</CCol>
              <CCol md="4" class="mb-3 mb-xl-0 text-right d-grid">
                <CButton
                  size="sm"
                  class="btn-bounce-email"
                  @click="csvDownload(lang.value)"
                  block
                  >エクセル
                  <CIcon name="download" style="margin: 0" />
                </CButton>
              </CCol>
              <CCol md="4" class="mb-3 mb-xl-0 text-right d-grid">
                <label
                  class="btn btn-success btn-sm btn-block"
                  style="line-height: inherit"
                >
                  エクセル{{ getFileNameBtn(excelFileInfo[lang.value]) }}
                  <CIcon name="upload" style="margin: 0" />
                  <input
                    type="file"
                    accept=".xlsx"
                    ref="excelFileImport"
                    @change="e => xlsxFileChange(e, lang.value)"
                    hidden
                  />
                </label>
              </CCol>
              <CCol md="2"></CCol>
            </CRow>
          </div>
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
        >
          <strong>画像一括登録</strong>
        </CCardHeader>
        <CCardBody>
          <CRow class="align-items-center mb-3">
            <CCol md="2">
              <div class="d-flex align-items-center gap-1">
                <span>画像ZIP</span>
                <div><ZipPopover /></div>
              </div>
            </CCol>
            <CCol md="4" class="mb-3 mb-xl-0 text-right d-grid">
              <label
                class="btn btn-primary btn-sm"
                style="line-height: inherit"
              >
                ZIP{{ getFileNameBtn(zipFileInfo) }}
                <CIcon name="upload" style="margin: 0" />
                <input
                  type="file"
                  accept=".zip"
                  ref="zipFileImport"
                  @change="zipFileChange"
                  hidden
                />
              </label>
            </CCol>
          </CRow>
        </CCardBody>

        <CCardFooter>
          <CButton color="light" class="mx-1" @click="goBack"
            >登録を中止して一覧に戻る</CButton
          >
          <CButton
            color="primary"
            class="mx-1"
            :disabled="loading || isReadOnly"
            @click="
              () => {
                confirmModal = true
                errMsgArray = []
              }
            "
            >登録する</CButton
          >
        </CCardFooter>
      </CForm>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="confirmModal"
      @close="
        () => {
          confirmModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="loading">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-else>
          <div v-if="errMsgArray?.length > 0">
            <p v-for="item in errMsgArray" :key="item">{{ item }}</p>
          </div>
          <div v-else>この内容で登録してもよろしいですか？</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <template v-if="errMsgArray?.length > 0">
          <CButton @click="confirmModal = false" color="primary"> OK </CButton>
        </template>
        <template v-else>
          <CButton
            :disabled="loading"
            @click="confirmModal = false"
            color="dark"
          >
            キャンセル
          </CButton>
          <CButton :disabled="loading" @click="register" color="primary">
            OK
          </CButton>
        </template>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="completeModal"
      @close="
        () => {
          completeModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 完了 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>
          <p>完了しました。</p>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              completeModal = false
              router.push({path: '/auctions'})
            }
          "
          color="primary"
          class="m-1"
        >
          OK
        </CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 更新中止確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              cancelModal = false
              btnClicked = false
              if (nextPage) nextPage(false)
            }
          "
          color="dark"
          >キャンセル
        </CButton>
        <CButton
          @click="
            () => {
              cancelModal = false
              if (nextPage) nextPage()
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="csvModal"
      @close="
        () => {
          csvModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingCsv"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="errMsgArray">
          <div v-for="(val, i) in errMsgArray" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton v-if="!loadingCsv" @click="closeCsvModal" color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
import Methods from '@/api/methods'
import DownloadFile from '@/api/uploadFileToS3'
import Base from '@/common/base'
import {CElementCover} from '@/components/Table'
import ScaleLoader from '@/components/Table/ScaleLoader.vue'
import {useAuthStore} from '@/store/auth'
import {computed, onMounted, ref, watch} from 'vue'
import {onBeforeRouteLeave, useRoute, useRouter} from 'vue-router'
import MemberSelectButton from '../../components/exhibitions/MemberSelectButton.vue'
import ZipPopover from './ZipPopover.vue'

  const router = useRouter()
  const route = useRoute()
  const {user, isReadOnly} = useAuthStore()

  const exhibitionList = ref([])
  const exhibitionData = ref({
    pitchOption: '0',
    pitchWidth: '',
    auctionClassification: '1',
    dealFlag: '0',
    bidCommitClassification: '2',
    extendFlag: '1',
    littleMoreDisplayFlag: '0',
    startDateTime: '',
    endDateTime: '',
    previewStartDateTime: '',
    previewEndDateTime: '',
    showBidCountFlag: '0',
    joinObject: '1',
  })
  const exhibitionNameJson = ref([])
  const constantList = ref([])
  const languageList = ref([])
  const languageListLoading = ref(false)
  const moreShowMember = ref(false)
  const auctionClassificationList = ref([])
  const bidCommitClassificationList = ref([])
  const pitchWidthList = ref([])
  const pitchOptions = ref([])
  const flagOptions = ref([
    {label: 'なし', value: '0'},
    {label: 'あり', value: '1'},
  ])
  const showBidCountFlagOptions = ref([
    {label: '非表示', value: '0'},
    {label: '表示', value: '1'},
  ])
  const dealBidPriceOptions = ref([
    {label: '使用する', value: '1'},
    {label: '使用しない', value: '0'},
  ])
  const startDate = ref('')
  const startTime = ref('')
  const endDate = ref('')
  const endTime = ref('')
  const previewStartDate = ref('')
  const previewStartTime = ref('')
  const previewEndDate = ref('')
  const previewEndTime = ref('')
  const extendDate = ref('')
  const extendTime = ref('')
  const loading = ref(true)
  const confirmModal = ref(false)
  const completeModal = ref(false)
  const cancelModal = ref(false)
  const changeFlag = ref(false)
  const dispDealFlag = ref(false)
  const errMsgArray = ref([])

  // CSV用
  const loadingCsv = ref(false)
  const csvModal = ref(false)
  const excelFileInfo = ref({})
  const zipFileInfo = ref()
  const btnClicked = ref(false)

  // Date validation
  const dateValidate = ref({
    previewStartDate: true,
    previewStartTime: true,
    previewEndDate: true,
    previewEndTime: true,
    startDate: true,
    startTime: true,
    endDate: true,
    endTime: true,
    extendDate: true,
    extendTime: true,
  })

  const bidMemberOptions = ref([
    {label: '全員', value: '1'},
    {label: '特定会員のみ', value: '2'},
  ])
  const allMembers = ref([])
  const selectedMembers = ref([])
  const selectedLanguage = ref('') // 言語選択用の変数を追加

  const nextPage = ref(null)

  const maxDate = ref('')

  const bidOption = ref(null)

  onBeforeRouteLeave((to, from, next) => {
    if (changeFlag.value) {
      nextPage.value = next
      cancelModal.value = true
    } else {
      // eslint-disable-next-line callback-return
      next()
    }
  })

  watch(
    () => exhibitionData,
    (newVal, oldVal) => {
      if (Object.keys(oldVal).length !== 0) {
        changeFlag.value = true
      }
    }
  )

  const getDealOption = computed(() => {
    return exhibitionData.value.dealFlag === '1'
      ? 'あり'
      : exhibitionData.value.dealFlag === '0'
        ? 'なし'
        : '-'
  })

  const getConstants = () => {
    // 初期化
    pitchOptions.value = []
    pitchWidthList.value = []
    auctionClassificationList.value = []
    bidCommitClassificationList.value = []

    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: [
        'AUCTION_CLASSIFICATION',
        'BID_COMMIT_CLASSIFICATION',
        'PITCH_WIDTH',
        'DEFAULT_PITCH_WIDTH',
        'PITCH_OPTION',
        'LANGUAGE_CODE',
      ],
    }).then(response => {
      if (response.status === 200) {
        constantList.value = response.data
        // Auction classification
        auctionClassificationList.value = (response.data || [])
          .filter(x => x.key_string === 'AUCTION_CLASSIFICATION')
          .map(x => ({label: x.value2, value: x.value1}))
        // 落札価格決定区分
        bidCommitClassificationList.value = (response.data || [])
          .filter(x => x.key_string === 'BID_COMMIT_CLASSIFICATION')
          .map(x => ({label: x.value2, value: x.value1}))
        // 入札単位
        pitchWidthList.value = (response.data || [])
          .filter(x => x.key_string === 'PITCH_WIDTH')
          .map(x => ({
            label: `${Number(x.value1).toLocaleString()}(${x.value2})`,
            value: x.value1,
          }))
        // 入札単位区分
        pitchOptions.value = (response.data || [])
          .filter(x => x.key_string === 'PITCH_OPTION')
          .map(x => ({label: x.value2, value: x.value1}))
      }
      return Promise.resolve()
    })
  }

  const getTenantOptions = () => {
    return Methods.apiExecute('get-tenant-options').then(response => {
      bidOption.value = response.data[0].bid_options
    })
  }

  const getExhibitions = () => {
    const search_condition = {
      preview_start_datetime_from: null,
      preview_start_datetime_to: null,
      start_datetime_from: null,
      start_datetime_to: null,
      previewFromDateFlag: true,
      previewToDateFlag: true,
      bidFromDateFlag: true,
      bidToDateFlag: true,
    }

    return Methods.apiExecute('get-exhibitions', search_condition).then(
      response => {
        if (response.status === 200) {
          for (const row of response.data) {
            if (row.localized_json_array) {
              for (const key of Object.keys(row.localized_json_array)) {
                if (row.localized_json_array[key].f1 === user.language_code) {
                  row.exhibition_name = row.localized_json_array[key].f2
                }
              }
            } else {
              row.exhibition_name = '未設定'
            }
          }
          const exhibitionList = response.data

          console.log('exhibitionList: ', exhibitionList)

          if (
            typeof exhibitionList === 'undefined' ||
            exhibitionList === null
          ) {
            return Promise.resolve(null)
          }
          return Promise.resolve(exhibitionList)
        }
        return Promise.resolve(null)
      }
    )
  }

  const validateData = inputParam => {
    // Firstly validating exhibition data
    const validateParam = {
      validation_mode: true,
      validation_excel: false,
      excel_urls: null,
    }
    const request = Object.assign({}, inputParam, validateParam)

    return Methods.apiExecute('upsert-exhibitions', request)
      .then(response1 => {
        // Upload excel file
        const s3data = {
          zip_url: null,
          excel_urls: {},
        }
        return Promise.resolve()
          .then(() => {
            if (excelFileInfo.value) {
              // Upload to s3
              console.log('upload excel file')
              return Promise.all(
                Object.keys(excelFileInfo.value).map(lang => {
                  return DownloadFile.uploadPromise(
                    'csv-upload',
                    excelFileInfo.value[lang]
                  ).then(data => {
                    console.log(`data. ${JSON.stringify(data)}`)
                    s3data.excel_urls[lang] = data.message
                    return Promise.resolve()
                  })
                })
              )
            }
            return Promise.resolve()
          })
          .then(() => {
            if (zipFileInfo.value) {
              // Upload to s3
              console.log('upload zip file')
              return DownloadFile.uploadPromise(
                'csv-upload',
                zipFileInfo.value
              ).then(data => {
                console.log(`data. ${JSON.stringify(data)}`)
                s3data.zip_url = data.message
                return Promise.resolve()
              })
            }
            return Promise.resolve()
          })
          .then(() => {
            return Promise.resolve(s3data)
          })
      })
      .then(s3data => {
        // Then validating excel file
        console.log('s3data: ', s3data)
        if (s3data) {
          request.validation_excel = true
          request.excel_urls = s3data.excel_urls
          request.zip_url = s3data.zip_url
          console.log('request: ', request)
          return Methods.apiExecute('upsert-exhibitions', request).then(res => {
            return Promise.resolve(s3data)
          })
        }
        return Promise.resolve(s3data)
      })
      .then(s3data => {
        console.log('Validated s3data: ', s3data)
        return Promise.resolve(s3data)
      })
  }

  const register = () => {
    loading.value = true
    errMsgArray.value = []

    // 多言語対応項目の整形
    exhibitionNameJson.value = []
    languageList.value.map(lang => {
      exhibitionNameJson.value.push({
        exhibition_name: exhibitionData.value[`exhibition_name_${lang.value}`]
          ? exhibitionData.value[`exhibition_name_${lang.value}`]
          : null,
        language_label: lang.label,
        language_value: lang.value,
      })
    })

    const request = {
      exhibition_no: null,
      showBidCountFlag: exhibitionData.value.showBidCountFlag,
      pitch_option: exhibitionData.value.pitchOption,
      exhibition_localized_json: exhibitionNameJson.value,
      preview_start_datetime: exhibitionData.value.previewStartDateTime?.trim()
        ? exhibitionData.value.previewStartDateTime
        : null,
      preview_end_datetime: exhibitionData.value.previewEndDateTime?.trim()
        ? exhibitionData.value.previewEndDateTime
        : null,
      start_datetime: exhibitionData.value.startDateTime?.trim()
        ? exhibitionData.value.startDateTime
        : null,
      end_datetime: exhibitionData.value.endDateTime?.trim()
        ? exhibitionData.value.endDateTime
        : null,
      max_extend_datetime:
        exhibitionData.value.extendFlag === '0' ||
        !exhibitionData.value.maxExtendDateTime ||
        !exhibitionData.value.maxExtendDateTime.trim()
          ? null
          : exhibitionData.value.maxExtendDateTime,
      extend_judge_minutes:
        exhibitionData.value.extendFlag === '0' ||
        !exhibitionData.value.extendJudgeMinutes
          ? null
          : exhibitionData.value.extendJudgeMinutes,
      extend_minutes:
        exhibitionData.value.extendFlag === '0' ||
        !exhibitionData.value.extendMinutes
          ? null
          : exhibitionData.value.extendMinutes,
      pitch_width: Number(Number(exhibitionData.value.pitchWidth).toFixed(2)),
      more_little_judge_pitch:
        exhibitionData.value.littleMoreDisplayFlag === '0' ||
        !exhibitionData.value.moreLittleJudgePitch
          ? null
          : exhibitionData.value.moreLittleJudgePitch,
      auctionClassification: exhibitionData.value.auctionClassification,
      dealFlag: exhibitionData.value.dealFlag,
      littleMoreDisplayFlag: exhibitionData.value.littleMoreDisplayFlag,
      bidCommitClassification: exhibitionData.value.bidCommitClassification,
      extendFlag: exhibitionData.value.extendFlag,
      previewStartDateTimeFlag:
        dateValidate.value.previewStartDate &&
        dateValidate.value.previewStartTime,
      startDateTimeFlag:
        dateValidate.value.startDate && dateValidate.value.startTime,
      endDateTimeFlag: dateValidate.value.endDate && dateValidate.value.endTime,
      previewEndDateTimeFlag:
        dateValidate.value.previewEndDate && dateValidate.value.previewEndTime,
      extendDateTimeFlag:
        dateValidate.value.extendDate && dateValidate.value.extendTime,
      joinObject: exhibitionData.value.joinObject,
      exhibitionMemberNos: selectedMembers.value.map(row => row.member_no)
    }

    validateData(request)
      .then(s3data => {
        // Validation successful then updating data
        request.validation_mode = false
        request.validation_excel = false
        request.excel_urls = s3data.excel_urls
        request.zip_url = s3data.zip_url
        console.log('request: ', request)

        return Methods.apiExecute('upsert-exhibitions', request).then(
          response => {
            console.log('UPDATE DATA SUCCESSFUL!')
            loading.value = false
            confirmModal.value = false
            completeModal.value = true
            changeFlag.value = false
          }
        )
      })
      .catch(error => {
        if (error.response.status === 400) {
          loading.value = false
          confirmModal.value = true
        } else {
          loading.value = false
          changeFlag.value = false
        }
        errMsgArray.value = Methods.parseHtmlResponseError(router, error)
      })
  }

  const changeStartDateTime = () => {
    exhibitionData.value.startDateTime =
      startDate.value && startTime.value
        ? `${startDate.value} ${startTime.value}`
        : null
  }
  const changeEndDateTime = () => {
    exhibitionData.value.endDateTime =
      endDate.value && endTime.value
        ? `${endDate.value} ${endTime.value}`
        : null
  }
  const changePreviewStartDateTime = () => {
    exhibitionData.value.previewStartDateTime =
      previewStartDate.value && previewStartTime.value
        ? `${previewStartDate.value} ${previewStartTime.value}`
        : null
  }

  const changePreviewEndDateTime = () => {
    exhibitionData.value.previewEndDateTime =
      previewEndDate.value && previewEndTime.value
        ? `${previewEndDate.value} ${previewEndTime.value}`
        : null
  }

  const changeMaxExtendDateTime = () => {
    exhibitionData.value.maxExtendDateTime = `${extendDate.value} ${extendTime.value}`
  }

  const goBack = () => {
    if (btnClicked.value) {
      return
    }
    btnClicked.value = true
    router.push({path: '/auctions'})
  }

  const csvDownload = lang => {
    console.log('csvDownload')

    csvModal.value = true
    loadingCsv.value = true
    errMsgArray.value = []

    const request = {
      language_code: lang,
      exhibition_no: route.params.id,
    }
    // Request to server
    Methods.apiExecute('export-items-xlsx-file', request)
      .then(response => {
        if (response.status === 200) {
          const csv = response.data
          console.log(`csv = ${JSON.stringify(csv)}`)
          window.location.href = csv.url
        }
        loadingCsv.value = false
        csvModal.value = false
      })
      .catch(error => {
        loadingCsv.value = false
        errMsgArray.value = Methods.parseHtmlResponseError(router, error)
      })
  }

  const xlsxFileChange = (event, lang) => {
    console.log('xlsxFileChange:', lang)
    excelFileInfo.value[lang] =
      event.target.files.length > 0 ? event.target.files[0] : null
    event.target.value = null
  }

  const zipFileChange = event => {
    console.log('zipFileChange')
    zipFileInfo.value =
      event.target.files.length > 0 ? event.target.files[0] : null
    event.target.value = null
  }

  const closeCsvModal = () => {
    console.log('closeCsvModal')
    csvModal.value = false
  }

  const getFileNameBtn = fileInfo => {
    return Base.getFileNameShorten(fileInfo)
  }

  const setDefaultDataAuction = () => {
    previewStartDate.value = previewStartDate.value || null
    previewStartTime.value = previewStartTime.value || null
    startDate.value = startDate.value || null
    startTime.value = startTime.value || null
    endDate.value = endDate.value || null
    endTime.value = endTime.value || null
    previewEndDate.value = previewEndDate.value || null
    previewEndTime.value = previewEndTime.value || null

    // 競り上げの場合
    exhibitionData.value.bidCommitClassification = '2' // Second price
    exhibitionData.value.pitchOption = '1' // 指定単位
    exhibitionData.value.extendFlag = '0'
    exhibitionData.value.extendJudgeMinutes = null
    exhibitionData.value.extendMinutes = null
    extendDate.value = null
    extendTime.value = null
    exhibitionData.value.littleMoreDisplayFlag = '0'
    // 入札単位
    const defaultPitchWidth = constantList.value.filter(
      x => x.key_string === 'DEFAULT_PITCH_WIDTH' && x.value1 === '1'
    )[0]
    exhibitionData.value.pitchWidth =
      pitchWidthList.value && pitchWidthList.value.length > 0
        ? pitchWidthList.value[0].value
        : defaultPitchWidth.value2

    changeStartDateTime()
    changeEndDateTime()
    changePreviewStartDateTime()
    changePreviewEndDateTime()
    changeMaxExtendDateTime()
  }

  const setDefaultDataTender = () => {
    previewStartDate.value = previewStartDate.value || null
    previewStartTime.value = previewStartTime.value || null
    startDate.value = startDate.value || null
    startTime.value = startTime.value || null
    endDate.value = endDate.value || null
    endTime.value = endTime.value || null
    previewEndDate.value = previewEndDate.value || null
    previewEndTime.value = previewEndTime.value || null

    // 封印の場合
    exhibitionData.value.bidCommitClassification = '1' // First price
    exhibitionData.value.pitchOption = '1' // 指定単位
    exhibitionData.value.extendFlag = '0'
    exhibitionData.value.extendJudgeMinutes = null
    exhibitionData.value.extendMinutes = null
    extendDate.value = null
    extendTime.value = null
    exhibitionData.value.littleMoreDisplayFlag = '0'
    // 入札単位
    const defaultPitchWidth = constantList.value.filter(
      x => x.key_string === 'DEFAULT_PITCH_WIDTH' && x.value1 === '1'
    )[0]
    exhibitionData.value.pitchWidth = defaultPitchWidth.value2

    changeStartDateTime()
    changeEndDateTime()
    changePreviewStartDateTime()
    changePreviewEndDateTime()
    changeMaxExtendDateTime()
  }

  const onClassificationChange = auctionClassification => {
    console.log('onClassificationChange: ', auctionClassification)
    /*
     * [オークション]から[テンダー]に変更した場合
     * [入札単位区分]
     *   [指定単位]を選択にし、[現在価格帯]を非活性にする
     * [入札単位]（ピッチ）が表示される
     */
    if (auctionClassification === '2') {
      if (String(bidOption.value?.instant_win_fuin) === '1') {
        dispDealFlag.value = true;
      } else {
        dispDealFlag.value = false;
        exhibitionData.value.dealFlag = '0';
      }
      setDefaultDataTender()
    }

    /*
     * [テンダー]から[オークション]に変更した場合
     * [入札単位区分]
     *   [現在価格帯]を活性にし、[現在価格帯]を選択する
     * [入札単位]（ピッチ）が非表示になる
     */
    if (auctionClassification === '1') {
      // Default values setting 方式: オークション
      if (String(bidOption.value?.instant_win_seri) === '1') {
        dispDealFlag.value = true;
      } else {
        dispDealFlag.value = false;
        exhibitionData.value.dealFlag = '0';
      }
      setDefaultDataAuction()
    }
  }

  const getExhibitionData = lang => {
    // 入札会名から既存データを読み込む
    const exhibition_name = exhibitionData.value[`exhibition_name_${lang}`]
    if (lang === 'ja' && exhibition_name) {
      const filter = exhibitionList.value.filter(
        x => x.exhibition_name === exhibition_name
      )
      if (filter.length > 0) {
        exhibitionData.value.auctionClassification = String(
          filter[0].exhibition_classification_info.auctionClassification
        )
        exhibitionData.value.bidCommitClassification = String(
          filter[0].exhibition_classification_info.bidCommitClassification
        )
        exhibitionData.value.extendFlag = String(
          filter[0].exhibition_classification_info.extendFlag
        )
        exhibitionData.value.littleMoreDisplayFlag = String(
          filter[0].exhibition_classification_info.littleMoreDisplayFlag
        )
        if (filter[0].start_datetime) {
          startDate.value = filter[0].start_datetime.split(' ')[0]
          startTime.value = filter[0].start_datetime.split(' ')[1]
          changeStartDateTime()
        }
        if (filter[0].end_datetime) {
          endDate.value = filter[0].end_datetime.split(' ')[0]
          endTime.value = filter[0].end_datetime.split(' ')[1]
          changeEndDateTime()
        }
        if (filter[0].preview_start_datetime) {
          previewStartDate.value =
            filter[0].preview_start_datetime.split(' ')[0]
          previewStartTime.value =
            filter[0].preview_start_datetime.split(' ')[1]
          changePreviewStartDateTime()
        }
        if (filter[0].preview_end_datetime) {
          previewEndDate.value = filter[0].preview_end_datetime.split(' ')[0]
          previewEndTime.value = filter[0].preview_end_datetime.split(' ')[1]
          changePreviewEndDateTime()
        }
        if (filter[0].max_extend_datetime) {
          extendDate.value = filter[0].max_extend_datetime.split(' ')[0]
          extendTime.value = filter[0].max_extend_datetime.split(' ')[1]
          changeMaxExtendDateTime()
        }
        if (exhibitionData.value.auctionClassification === '2') {
          exhibitionData.value.bidCommitClassification =
            bidCommitClassificationList.value[0].value
          exhibitionData.value.extendFlag = '0'
          exhibitionData.value.littleMoreDisplayFlag = '0'
          exhibitionData.value.pitchWidth = constantList.value.filter(
            x =>
              x.key_string === 'DEFAULT_PITCH_WIDTH' &&
              x.value1 === exhibitionData.value.auctionClassification
          )[0].value2
          for (const i of document.getElementsByName('extend_flag')) {
            i.disabled = true
          }
          for (const i of document.getElementsByName(
            'little_more_display_flag'
          )) {
            i.disabled = true
          }
        } else {
          exhibitionData.value.extendJudgeMinutes =
            filter[0].extend_judge_minutes
          exhibitionData.value.extendMinutes = filter[0].extend_minutes
          exhibitionData.value.moreLittleJudgePitch =
            filter[0].more_little_judge_pitch
          exhibitionData.value.bidCommitClassification =
            bidCommitClassificationList.value[1].value
          exhibitionData.value.pitchWidth = constantList.value.filter(
            x =>
              x.key_string === 'DEFAULT_PITCH_WIDTH' &&
              x.value1 === exhibitionData.value.auctionClassification
          )[0].value2
          for (const i of document.getElementsByName('extend_flag')) {
            i.disabled = false
          }
          for (const i of document.getElementsByName(
            'little_more_display_flag'
          )) {
            i.disabled = false
          }
        }
      }
    }
  }
  const getLanguageList = () => {
    languageListLoading.value = true
    return Methods.apiExecute('get-tenant-language-list', {})
      .then(response => {
        if (response.status === 200) {
          // 言語
          languageList.value = response.data.language_code_list.map(cd => {
            const lang = constantList.value.find(
              item => item.key_string === 'LANGUAGE_CODE' && item.value1 === cd
            )
            if (lang) {
              return {
                value: lang.value1,
                label: lang?.value2 || '',
              }
            } else {
              return Promise.reject([`言語コード ${cd} が設定されていません。`])
            }
          })
          return Promise.resolve()
        }
        return Promise.resolve()
      })
      .finally(() => {
        languageListLoading.value = false
      })
  }

  const getMaxDate = () => {
    // Prettier-ignore
    const today = new Date(
      Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
    )
    const nextYearDate = new Date(today)
    nextYearDate.setDate(today.getDate() + 364)
    nextYearDate.setHours(0, 0, 0, 0) // 時間をリセットして日付のみ取得

    // YYYY-MM-DD形式で取得
    const year = nextYearDate.getFullYear()
    const month = String(nextYearDate.getMonth() + 1).padStart(2, '0')
    const day = String(nextYearDate.getDate()).padStart(2, '0')

    maxDate.value = `${year}-${month}-${day}`
  }

  onMounted(async () => {
    try {
      loading.value = true
      await getConstants()
      await getLanguageList()
      await getTenantOptions()
      const exhs = await getExhibitions()
      exhibitionList.value = exhs
      const res = await Methods.apiExecute('search-members', {})
      allMembers.value = res.data
      console.log('bidOption: ', bidOption.value)
      console.log('exhibitions', exhs)
      console.log('exhibitionList: ', exhibitionList.value)
      // Set default data: Sealed bid auction
      if (auctionClassificationList.value.length > 0) {
        const sealedBidAuction = auctionClassificationList.value.find(
          x => x.value === '2'
        )
        exhibitionData.value.auctionClassification = sealedBidAuction
          ? sealedBidAuction.value
          : auctionClassificationList.value[0].value
      }
      onClassificationChange(exhibitionData.value.auctionClassification)
      // 1年後の日付を取得
      getMaxDate()
      // デフォルト言語を設定
      if (languageList.value.length > 0) {
        selectedLanguage.value = languageList.value[0].value
      }
    } catch (error) {
      console.log(error)
      Methods.parseHtmlResponseError(router, error)
    } finally {
      loading.value = false
    }
  })
</script>

<style scoped>
  label {
    line-height: 35px;
  }
</style>
