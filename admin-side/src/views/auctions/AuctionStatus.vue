<template>
  <div class="mb-3">
    <CCard>
      <CCardBody>
        <CForm>
          <CCard>
            <CCardHeader>
              <strong>入札会情報</strong>
            </CCardHeader>
            <CCardBody>
              <CRow class="mb-3 g-3">
                <CCol md="3">
                  <label>入札会名</label>
                </CCol>
                <CCol md="9">
                  <div>
                    <a>{{ bidName }}</a>
                  </div>
                </CCol>
              </CRow>
              <CRow class="mb-3 g-3">
                <CCol md="3">
                  <label>ステータス</label>
                </CCol>
                <CCol md="9">
                  <div>
                    <a>{{ status }}</a>
                  </div>
                </CCol>
              </CRow>
              <CRow class="g-3">
                <CCol md="3">
                  <label>入札会日時</label>
                </CCol>
                <CCol md="9">
                  <div>
                    <a>{{ bidDateTime }}</a>
                  </div>
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
          <CCard class="mt-3">
            <CCardHeader>
              <strong>検索条件</strong>
            </CCardHeader>
            <CCardBody>
              <CRow class="mb-2">
                <CCol sm="2">
                  <label>ステータス</label>
                </CCol>
                <CCol sm="6">
                  <CFormCheck
                    v-for="option in optionsStatus.filter(x => x.visible)"
                    :key="option.value"
                    type="checkbox"
                    :id="option.value"
                    :value="option.value"
                    :label="option.label"
                    v-model="search_condition.status"
                    inline
                  />
                </CCol>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="2">
                  <label>商品名</label>
                </CCol>
                <CCol sm="6">
                  <CFormInput
                    name="nickname"
                    v-model="search_condition.productName"
                  />
                </CCol>
              </CRow>
              <CRow>
                <CCol sm="5"></CCol>
                <CCol sm="2" class="d-grid">
                  <CButton
                    size="sm"
                    color="info"
                    @click="getExhibitionStatusData"
                    :disabled="buttonLoading"
                    >検索</CButton
                  >
                </CCol>
                <CCol sm="5"></CCol>
              </CRow>
            </CCardBody>
          </CCard>
        </CForm>
      </CCardBody>
    </CCard>
    <CCard class="mt-3">
      <CCardHeader class="form-inline" style="display: block">
        <CIcon name="cil-grid" />入札一覧
        <span style="float: right; margin-left: 30px"
          >検索結果: {{ currentCount }}件</span
        >
        <span style="float: right">総件数: {{ totalCount }}件</span>
      </CCardHeader>
      <CCardBody>
        <CDataTable
          hover
          striped
          border
          sorter
          small
          fixed
          style="font-size: 14px"
          :sorter-value="itemsSorter"
          :items="exhibitionStatusList"
          :fields="dynamicFields"
          :items-per-page="30"
          :loading="loading"
          @update:sorter-value="sorterChange"
        >
          <template #lotNo="{item}">
            <td class="break-line" style="width: 85px">{{ item.lotNo }}</td>
          </template>
          <template #equipInforSort="{item}">
            <td class="break-line" style="width: 240px; margin-right: 10px">
              <div
                v-for="itemEqui in item.equipInforList"
                :key="itemEqui.index"
              >
                <div v-if="itemEqui.index == 0 || item.breakdownFlag">
                  <hr v-if="itemEqui.index != 0" />
                  <CRow>
                    <CCol>商品ID：{{ item.manageNo }}</CCol>
                  </CRow>
                  <div v-for="itemList in itemEqui.localized_product_name_list">
                    <CRow>
                      <CCol>【{{ itemList.language }}】</CCol>
                    </CRow>
                    <CRow>
                      <CCol>{{  itemList.logical_name }}：{{ itemList.localized_product_name }}</CCol>
                    </CRow>
                  </div>
                </div>
              </div>
              <br />
              <div v-if="!item.breakdownFlag && item.equipInforList.length > 1">
                <span class="link-text" @click="item.breakdownFlag = true"
                  >内訳を見る({{ item.equipInforList.length }})</span
                >
              </div>
              <div v-if="item.breakdownFlag">
                <span class="link-text" @click="item.breakdownFlag = false"
                  >内訳を閉じる</span
                >
              </div>
            </td>
          </template>
          <template #lowestBidInfo="{item}">
            <td class="break-line" style="text-align: left; width: 90px">
              <CRow>
                <CCol>{{ `${priceString(item.lowestBidPrice)} ${unit}` }}</CCol>
              </CRow>
            </td>
          </template>
          <template #lowestBidAcceptInfo="{item}">
            <td class="text-left" style="width: 90px">
              <CRow>
                <CCol>{{
                  `${priceString(item.lowestBidAcceptPrice)} ${unit}`
                }}</CCol>
              </CRow>
            </td>
          </template>
          <template #dealBidInfo="{item}">
            <td class="text-left" style="width: 90px">
              <CRow>
                <CCol>{{
                  `${priceString(item.dealBidPrice)} ${unit}`
                }}</CCol>
              </CRow>
            </td>
          </template>
          <template #currentPriceSort="{item}">
            <td class="break-line" style="text-align: right; width: 120px">
              <template v-if="item.auctionClassification === '2'"> - </template>
              <template v-else>
                <div v-if="item.status === '未入札'">
                  　{{
                    [
                      `${priceString(item.lowestBidPrice)} ${unit}`,
                      '\n',
                      '(最低入札価格)',
                    ].join(' ')
                  }}
                </div>
                <div v-else>{{ item.currentPrice }}</div>
              </template>
            </td>
          </template>
          <template #statusNumberBid="{item}">
            <td style="width: 120px">
              <span v-if="item.negotiationFlag === 1" class="break-line red"
                >商談完了</span
              >
              <span
                v-else-if="item.negotiationFlag === 2"
                class="break-line blue"
                >商談中</span
              >
              <div v-else>
                <span
                  v-bind:class="{
                    'break-line': true,
                    red: item.status == '最低落札越え',
                    orange: item.status == 'あと少し',
                    blue: item.status == '入札あり',
                    gray: item.status == '未入札',
                  }"
                  >{{ item.status }}</span
                >
                <br />
                <span v-if="item.numberBid">{{ item.numberBid }}</span>
              </div>
              <div class="mt-3 d-grid gap-1">
                <CButton
                  v-if="item.negotiationFlag === 0"
                  size="sm"
                  @click="() => openNegotiateDialog(item.exhibitionItemNo, 2)"
                  color="info"
                  :disabled="item.hummerFlag !== 0"
                  >商談中にする</CButton
                >
                <CButton
                  v-if="item.negotiationFlag === 2"
                  size="sm"
                  @click="() => openNegotiateDialog(item.exhibitionItemNo, 0)"
                  color="info"
                  :disabled="item.hummerFlag !== 0"
                  >商談中を解除する</CButton
                >
                <CButton
                  v-if="
                    item.negotiationFlag === 0 || item.negotiationFlag === 2
                  "
                  size="sm"
                  @click="() => openNegotiateDialog(item.exhibitionItemNo, 1)"
                  color="info"
                  :disabled="item.hummerFlag !== 0"
                  >商談を完了する</CButton
                >
              </div>
            </td>
          </template>
          <template #topBidMemberSort="{item}">
            <td class="break-line" style="width: 90px">
              {{ item.topBidMember }}
            </td>
          </template>
          <template #twoBidMemberSort="{item}">
            <td class="break-line" style="width: 90px">
              {{ item.twoBidMember }}
            </td>
          </template>
          <template #allRankDisplay="{item}">
            <td style="width: 80px" class="text-center">
              <CButton
                v-if="item.display"
                size="sm"
                @click="openBidNumberModal(item.rankingList)"
                block
                color="info"
                :disabled="buttonLoading"
                >表示</CButton
              >
            </td>
          </template>
        </CDataTable>
        <CRow class="bottom-menu sticky">
          <CCol sm="3">
            <CButton
              color="light"
              style="position: absolute; left: 0"
              @click="goBack"
            >
              入札会一覧に戻る
            </CButton>
          </CCol>
          <CCol sm="9">
            <CRow class="d-grid gap-2 d-md-flex justify-content-md-end">
              <CButton
                color="info"
                class="float-right"
                style="width: 120px; margin-left: 10px; margin-right: 10px"
                @click="getExhibitionStatusData"
                :disabled="buttonLoading"
              >
                リロード
              </CButton>
              <CButton
                color="success"
                class="float-right"
                style="width: 120px; margin-left: 10px; margin-right: 10px"
                @click="(currentButton = true), downloadFile(1, false)"
                :disabled="buttonLoading || currentBidCount === 0"
              >
                入札履歴
              </CButton>
              <CButton
                color="success"
                class="float-right"
                style="width: 120px; margin-left: 10px; margin-right: 10px"
                @click="(currentButton = true), downloadFile(2, false)"
                :disabled="buttonLoading || currentBidCount === 0"
              >
                入札順位
              </CButton>
              <CButton
                color="success"
                class="float-right"
                style="width: 120px; margin-left: 10px; margin-right: 10px"
                @click="(currentButton = true), downloadFile(3, false)"
                :disabled="buttonLoading || currentBidCount === 0"
              >
                入札結果
              </CButton>
            </CRow>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>

    <CModal
      size="lg"
      backdrop="static"
      :keyboard="false"
      :visible="bidNumberListModal"
      @close="
        () => {
          bidNumberListModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入札一覧</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>
          <CDataTable
            :loading="loading"
            hover
            striped
            border
            sorter
            :items="bidNumberList"
            :fields="bidFields"
            :sorterValue="{asc: true, column: 'rankNo'}"
          >
            <template #bidPrice="{item}">
              <td style="text-align: right">{{ item.bidPrice }}</td>
            </template>
            <template #bidQuantity="{item}">
              <td style="text-align: right">{{ item.bidQuantity }}</td>
            </template>
            <template #bidSuccessQuantity="{item}">
              <td style="text-align: right">{{ item.bidSuccessQuantity }}</td>
            </template>
          </CDataTable>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="bidNumberListModal = false" color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>エラー確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-for="(text, i) in errorMsg" :key="i">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="(errorModal = false), (errorMsg = '')" color="dark"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="bidHistoryConfirmModal"
      @close="
        () => {
          bidHistoryConfirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入札履歴確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="d-flex justify-content-around">
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = true), downloadFile(1, false)"
            :disabled="currentBidCount === 0"
            :class="currentBidCount === 0 ? 'btn-download-disabled' : ''"
          >
            現在の入札会
          </CButton>
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = false), downloadFile(1, true)"
            :disabled="similarBidCount === 0"
            :class="similarBidCount === 0 ? 'btn-download-disabled' : ''"
          >
            同一入札会名
          </CButton>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="bidHistoryConfirmModal = false"
          :disabled="buttonLoading"
          color="dark"
          >キャンセル</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="bidBidOrderConfirmModal"
      @close="
        () => {
          bidBidOrderConfirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入札順位確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="d-flex justify-content-around">
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = true), downloadFile(2, false)"
            :disabled="currentBidCount === 0"
            :class="currentBidCount === 0 ? 'btn-download-disabled' : ''"
          >
            現在の入札会
          </CButton>
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = false), downloadFile(2, true)"
            :disabled="similarBidCount === 0"
            :class="similarBidCount === 0 ? 'btn-download-disabled' : ''"
          >
            同一入札会名
          </CButton>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="bidBidOrderConfirmModal = false"
          :disabled="buttonLoading"
          color="dark"
          >キャンセル</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="bidBidResultsConfirmModal"
      @close="
        () => {
          bidBidResultsConfirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入札会結果</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="d-flex justify-content-around">
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = true), downloadFile(3, false)"
            :disabled="currentStatus === '落札結果未確定'"
            :class="
              currentStatus === '落札結果未確定' ? 'btn-download-disabled' : ''
            "
          >
            現在の入札会
          </CButton>
          <CButton
            color="bounce-email"
            style="width: 120px; margin-left: 10px; margin-right: 10px"
            @click="(currentButton = false), downloadFile(3, true)"
            :disabled="similarStatus === '落札結果未確定'"
            :class="
              similarStatus === '落札結果未確定' ? 'btn-download-disabled' : ''
            "
          >
            同一入札会名
          </CButton>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="bidBidResultsConfirmModal = false"
          :disabled="buttonLoading"
          color="dark"
          >キャンセル</CButton
        >
      </CModalFooter>
    </CModal>
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isShowConfirmDialog"
      @close="
        () => {
          isShowConfirmDialog = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入札会結果</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>
          <div v-if="errorMsg && errorMsg.length > 0">
            <span v-for="err in errorMsg">{{ err }}</span>
          </div>
          <span v-else>{{ negoContentText }}</span>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="isShowConfirmDialog = false"
          color="dark"
          :disabled="loading"
          >閉じる</CButton
        >
        <CButton
          v-if="!(errorMsg && errorMsg.length > 0)"
          @click="negotiate"
          color="primary"
          :disabled="loading"
          >OK
        </CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  const bidFields = [
    {key: 'rankNo', label: 'No', _classes: 'text-center'},
    {key: 'companyName', label: '会社名・貿易名', _style: 'text-align: center'},
    {key: 'bidPrice', label: '入札価格', _style: 'text-align: center;'},
    // {key : 'bidQuantity', label : '入札数量', _style : 'text-align: center;'},
    {
      key: 'bidSuccessQuantity',
      label: '落札価格',
      _style: 'text-align: center;',
    },
  ];

  const unit = '円';
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {CDataTable} from '@/components/Table/index';
import {useAuthStore} from '@/store/auth';
import {useCommonStore} from '@/store/common';

  export default {
    name: 'ExhibitionStatus',
    components: {
      CDataTable,
    },
    setup() {
      const store = useCommonStore();
      return {store};
    },
    data() {
      return {
        authStore: useAuthStore(),
        loading: true,
        buttonLoading: false,
        optionsStatus: [
          {value: '0', label: '未入札', visible: true},
          {value: '2', label: '最低落札越え', visible: true},
          {value: '1', label: '入札あり', visible: true},
        ],
        unit,
        bidFields,
        bidNumberListModal: false,
        bidHistoryConfirmModal: false,
        bidBidOrderConfirmModal: false,
        bidBidResultsConfirmModal: false,
        bidName: '',
        exhibitionNoList: '',
        method: '',
        status: '',
        bidDateTime: '',
        validDealBidPrice: false,
        bidNumberList: [],
        constantList: [],
        constLanguageOptions: [],
        exhibitionStatusList: [],
        itemsSorter: {asc: true, column: 'equipInforSort'},
        itemsSorterBid: {asc: false, column: 'rankNo'},
        errorModal: false,
        // 検索条件
        search_condition: {
          status: ['0', '1', '2'],
          productName: '',
        },
        currentButton: true,
        currentBidCount: 0,
        similarBidCount: 0,
        currentStatus: '落札結果未確定',
        similarStatus: '落札結果未確定',
        errorMsg: [],

        // Counting
        current_count: 0,
        total_count: 0,

        // Confirm dialog
        isShowConfirmDialog: false,
        negoContentText: '',
        negoExhibitionItemNo: null,
        negotiationFlag: null,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        vm.store.set(['itemsSorter', {asc: true, column: 'equipInforSort'}]);
      });
    },
    mounted() {
      // This.getExhibitionStatusData()
      this.getConstants();
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query?.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['exhibitionsSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.exhibitionList.length > newVal) {
          this.pages =
            Number.parseInt(this.exhibitionList.length / newVal, 10) +
            (this.exhibitionList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
      dynamicFields() {
        const baseFields = [
          {key: 'equipInforSort', label: '商品情報', _classes: 'text-center'},
          {
            key: 'lowestBidInfo',
            label: '最低入札情報',
            _classes: 'text-center  break-white-space',
          },
          {
            key: 'lowestBidAcceptInfo',
            label: '最低落札情報',
            _classes: 'text-center  break-white-space',
          },
        ];

        if (this.validDealBidPrice) {
          baseFields.push({
            key: 'dealBidInfo',
            label: '即決価格',
            _classes: 'text-center  break-white-space',
          });
        }

        baseFields.push(
          {key: 'currentPriceSort', label: '現在価格', _classes: 'text-center'},
          {
            key: 'statusNumberBid',
            label: 'ステータス入札件数',
            _classes: 'text-center',
          },
          {key: 'topBidMemberSort', label: 'TOP入札会員', _classes: 'text-center'},
          {key: 'twoBidMemberSort', label: '２nd入札会員', _classes: 'text-center'},
          {key: 'allRankDisplay', label: '全順位表示', _classes: 'text-center'},
        );
        return baseFields;
      }
    },
    methods: {
      getExhibitionStatusData() {
        this.loading = true;
        this.buttonLoading = true;

        this.current_count = 0;
        this.total_count = 0;

        const request = {
          exhibitionNos: [this.$route.params.id],
          productName: this.search_condition.productName || null,
          status: this.search_condition.status || null,
        };
        Methods.apiExecute('get-exhibition-status-list', request)
          .then(response => {
            if (response.status === 200) {
              this.total_count = response.data
                ? response.data.total_count || 0
                : 0;
              this.current_count = response.data
                ? response.data.current_count || 0
                : 0;

              this.exhibitionStatusList = [];
              response.data.data.map(exhibition => {
                // Calc order for sorting
                const orderCount = exhibition.manage_no;
                // Item list
                const equipInforList = exhibition.item_list.map(
                  (currentValue, index) => {
                    return {
                      index,
                      localized_product_name_list: exhibition.item_name_list.map(item => {
                        return {
                          logical_name: item.logical_name,
                          localized_product_name: item.localized_product_name,
                          language: this.getLanguageLabel(item.language_code)
                        }
                      }),
                    };
                  }
                );
                // Top bid member
                const topBidMemberSort =
                  exhibition.ranking_list && exhibition.ranking_list.length > 0
                    ? exhibition.ranking_list[0].bid_price || 0
                    : 0;
                const topBidMember =
                  exhibition.ranking_list && exhibition.ranking_list.length > 0
                    ? [
                        exhibition.ranking_list[0].company_name
                          ? exhibition.ranking_list[0].company_name
                          : '',
                        exhibition.ranking_list[0].bid_price
                          ? `${this.priceString(exhibition.ranking_list[0].bid_price)} ${unit}`
                          : '',
                      ]
                        .filter(word => word.length > 0)
                        .join('\n')
                    : '';
                // 2nd member
                const twoBidMemberSort =
                  exhibition.ranking_list && exhibition.ranking_list.length > 1
                    ? exhibition.ranking_list[1].bid_price || 0
                    : 0;
                const twoBidMember =
                  exhibition.ranking_list && exhibition.ranking_list.length > 1
                    ? [
                        exhibition.ranking_list[1].company_name
                          ? exhibition.ranking_list[1].company_name
                          : '',
                        exhibition.ranking_list[1].bid_price
                          ? `${this.priceString(exhibition.ranking_list[1].bid_price)} ${unit}`
                          : '',
                      ]
                        .filter(word => word.length > 0)
                        .join('\n')
                    : '';

                this.exhibitionStatusList.push({
                  lotNo: exhibition.lot_id,
                  manageNo: exhibition.manage_no,
                  negotiationFlag: exhibition.negotiation_flag,
                  auctionClassification: exhibition.auction_classification,
                  equipInforSort: orderCount,
                  equipInforList,
                  lowestBidQuantiy: exhibition.lowest_bid_quantity,
                  lowestBidAcceptQuantity:
                    exhibition.lowest_bid_accept_quantity,
                  lowestBidPrice: exhibition.lowest_bid_price,
                  lowestBidAcceptPrice: exhibition.lowest_bid_accept_price,
                  dealBidPrice: exhibition.deal_bid_price,
                  currentPriceSort: exhibition.current_price,
                  currentPrice: `${this.priceString(exhibition.current_price)} ${unit}`,
                  status: this.optionsStatus.find(
                    x => x.value === String(exhibition.exhibition_item_status)
                  )?.label,
                  numberBid: this.optionsStatus.find(
                    x => x.value === String(exhibition.exhibition_item_status)
                  )?.value
                    ? `${exhibition.bid_count} 件：${exhibition.bid_member_count} 会員`
                    : null,
                  topBidMemberSort,
                  topBidMember,
                  twoBidMemberSort,
                  twoBidMember,
                  extendedTime:
                    exhibition.extend_flag === 1
                      ? exhibition.end_datetime
                      : null,
                  extendedTimeFlag: exhibition.extend_flag === 1,
                  display: exhibition.bid_count > 0,
                  rankingList: exhibition.ranking_list,
                  breakdownFlag: false,
                  hummerFlag: exhibition.hummer_flag,
                });
              });

              console.log('exhibitionStatusList: ', this.exhibitionStatusList);
              return Promise.resolve();
            }
            return Promise.resolve();
          })
          .then(() => {
            return this.getExhibitionInfor();
          })
          .then(() => {
            return this.getExhibitionSummaryByName();
          })
          .then(() => {
            return this.getExhibitionSummary();
          })
          .then(() => {
            this.loading = false;
            this.buttonLoading = false;
          })
          .catch(error => {
            this.loading = false;
            this.buttonLoading = false;
            this.errorModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getExhibitionSummary() {
        this.loading = true;
        this.buttonLoading = true;
        const request = {
          exhibitionNo:
            this.exhibitionNoList.length > 0
              ? this.exhibitionNoList
              : [this.$route.params.id],
        };
        Methods.apiExecute('get-exhibition-summary', request)
          .then(response => {
            if (response.status === 200) {
              console.log('getExhibitionSummary: ', response.data);
              this.currentStatus = '落札結果未確定';
              this.similarStatus = '落札結果未確定';
              this.currentBidCount =
                response.data.filter(
                  x =>
                    Number.parseInt(x.exhibition_no, 10) ===
                    Number.parseInt(this.$route.params.id, 10)
                ).length > 0
                  ? response.data.filter(
                      x =>
                        Number.parseInt(x.exhibition_no, 10) ===
                        Number.parseInt(this.$route.params.id, 10)
                    )[0].bid_count
                  : 0;
              this.currentStatus =
                response.data.filter(
                  x =>
                    Number.parseInt(x.exhibition_no, 10) ===
                    Number.parseInt(this.$route.params.id, 10)
                ).length > 0
                  ? response.data.filter(
                      x =>
                        Number.parseInt(x.exhibition_no, 10) ===
                        Number.parseInt(this.$route.params.id, 10)
                    )[0].status
                  : '落札結果未確定';
              this.similarStatus =
                response.data.filter(x => x.status !== '落札結果未確定')
                  .length > 0
                  ? response.data.filter(x => x.status !== '落札結果未確定')[0]
                      .status
                  : '落札結果未確定';
              this.similarBidCount = response.data.reduce((sum, item) => {
                return sum + item.bid_count;
              }, 0);
              this.loading = false;
            }
            this.buttonLoading = false;
          })
          .catch(error => {
            this.loading = false;
            this.buttonLoading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getExhibitionInfor() {
        const request = {
          exhibition_no: this.$route.params.id,
          previewFromDateFlag: true,
          previewToDateFlag: true,
          bidFromDateFlag: true,
          bidToDateFlag: true,
        };
        return Methods.apiExecute('get-exhibitions', request).then(response => {
          if (response.status === 200 && response.data.length > 0) {
            const data = response.data[0];
            console.log('getExhibitionInfor: ', data);
            const bidItem = data.localized_json_array.find(
              x => x.f1 === this.authStore.user.language_code
            );
            this.bidName = bidItem ? bidItem.f2 : '';
            const methodItem = this.constantList.find(
              x =>
                x.key_string === 'AUCTION_CLASSIFICATION' &&
                x.value1 ===
                  data.exhibition_classification_info.auctionClassification &&
                x.language_code === this.authStore.user.language_code
            );
            this.method = methodItem ? methodItem.value2 : '';
            this.status = data.status;
            const start_datetime = data.start_datetime
              ? Methods.getFormatDateTime(data.start_datetime)
              : Methods.getFormatDateTime(data.preview_start_datetime);
            const end_datetime = data.end_datetime
              ? Methods.getFormatDateTime(data.end_datetime)
              : Methods.getFormatDateTime(data.preview_end_datetime);
            this.bidDateTime = `${start_datetime} ~ ${end_datetime}`;
            this.validDealBidPrice = data.exhibition_classification_info.dealFlag === 1 ? true : false;
          }
          return Promise.resolve();
        });
      },
      downloadFile(type, by_name) {
        console.log('downloadFile');
        if (this.loading) {
          return;
        }
        this.loading = true;
        this.buttonLoading = true;

        Promise.resolve()
          .then(() => {
            if (!by_name) {
              return Promise.resolve();
            }
            const request = {
              exhibitionName: this.bidName,
            };
            return Methods.apiExecute('get-exhibition-by-name', request).then(
              response => {
                if (response.status === 200) {
                  this.exhibitionNoList = [];
                  response.data.map(exhibition => {
                    this.exhibitionNoList.push(exhibition.exhibition_no);
                  });
                }
                return Promise.resolve();
              }
            );
          })
          .then(() => {
            let api_name = null;
            switch (type) {
              case 1:
                api_name = 'get-bid-history';
                break;
              case 2:
                api_name = 'get-bid-order';
                break;
              case 3:
                api_name = 'get-bid-result';
                break;
              default:
                break;
            }

            const request = {
              exhibitionNo: this.currentButton
                ? [this.$route.params.id]
                : this.exhibitionNoList,
            };
            return Methods.apiExecute(api_name, request).then(response => {
              return Promise.resolve(response.data);
            });
          })
          .then(data => {
            this.loading = false;
            this.exhibitionNoList = [];
            this.buttonLoading = false;
            this.bidHistoryConfirmModal = false;
            this.bidBidOrderConfirmModal = false;
            this.bidBidResultsConfirmModal = false;
            if (data?.url) {
              window.location.href = data.url;
            }
          })
          .catch(error => {
            this.loading = false;
            this.exhibitionNoList = [];
            this.buttonLoading = false;
            this.bidHistoryConfirmModal = false;
            this.errorModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getExhibitionSummaryByName() {
        const request = {
          exhibitionName: this.bidName,
        };
        return Methods.apiExecute('get-exhibition-by-name', request).then(
          response => {
            if (response.status === 200) {
              this.exhibitionNoList = [];
              response.data.map(exhibition => {
                this.exhibitionNoList.push(exhibition.exhibition_no);
              });
            }
            return Promise.resolve();
          }
        );
      },
      getConstants() {
        this.buttonLoading = true;
        const request = {
          key_strings: ['AUCTION_CLASSIFICATION', 'LANGUAGE_CODE'],
        };
        Methods.apiExecute('get-constants-by-keys-language', request)
          .then(response => {
            if (response.status === 200) {
              this.constantList = response.data;
              const languageList = this.constantList.filter(
                x => x.key_string === 'LANGUAGE_CODE'
              );
              this.constLanguageOptions = languageList.map(item => ({
                value: item.value1,
                label: item.value2,
              }));
              this.getExhibitionStatusData();
            }
          })
          .catch(error => {
            this.errorModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getLanguageLabel(languageCode) {
        const language = this.constLanguageOptions.find(
          lang => lang.value === languageCode
        );
        return language ? language.label : '';
      },
      timeRemainingCal(seconds, showLessThanMinutes) {
        let result = '';
        let min = '';
        if (seconds > 0) {
          const day = Math.floor(seconds / 60 / 60 / 24);
          const hour = Math.floor((seconds / 60 / 60) % 24);
          if (showLessThanMinutes && seconds > 3600) {
            min = Math.floor(seconds / 60);
          } else {
            min = Math.floor((seconds / 60) % 60);
          }
          const sec = Math.floor(seconds % 60);
          if (!showLessThanMinutes) {
            if (day >= 2) {
              result += `${day}日 `;
            } else if (day >= 1) {
              result += `${day}日 `;
            }
            if (hour < 10) {
              if (hour >= 1) {
                result += `0${hour}:`;
              }
            } else {
              result += `${hour}:`;
            }
          }
          if (min < 10) {
            result += `0${min}:`;
          } else {
            result += `${min}:`;
          }
          if (sec < 10) {
            result += `0${sec}`;
          } else {
            result += sec;
          }
        }
        return result;
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
      },
      openBidNumberModal(rankingList) {
        this.bidNumberList = [];
        let no = 1;
        rankingList.map(item => {
          this.bidNumberList.push({
            rankNo: no++,
            companyName: item.company_name || '',
            bidPrice: item.bid_price
              ? `${this.priceString(item.bid_price)} ${unit}`
              : '',
          });
        });
        this.itemsSorterBid = {asc: false, column: 'rankNo'};
        this.bidNumberListModal = true;
      },
      goBack() {
        this.$router.push({name: '入札会一覧', params: {back_flag: true}});
      },
      priceString(price) {
        return Base.priceLocaleString(price) || 0;
      },
      openNegotiateDialog(exhibitionItemNo, negotiationFlag) {
        this.errorMsg = [];
        if (negotiationFlag === 2) {
          this.negoContentText = '商談中にしてもよろしいですか？';
        } else if (negotiationFlag === 0) {
          this.negoContentText = '商談中を解除してもよろしいですか？';
        } else if (negotiationFlag === 1) {
          this.negoContentText = '商談を完了してもよろしいですか？';
        }
        this.isShowConfirmDialog = true;
        this.negoExhibitionItemNo = exhibitionItemNo;
        this.negotiationFlag = negotiationFlag;
      },
      negotiate() {
        console.log(
          'negotiate',
          this.negoExhibitionItemNo,
          this.negotiationFlag
        );
        this.loading = true;
        const request = {
          exhibition_item_no: this.negoExhibitionItemNo,
          negotiation_flag: this.negotiationFlag,
        };
        Methods.apiExecute('update-negotiation-flag', request)
          .then(response => {
            console.log('response', response);
            this.loading = false;
            this.isShowConfirmDialog = false;
            this.getExhibitionStatusData();
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
    },
  };
</script>
<style lang="scss">
  .break-line.red {
    color: red;
  }

  .break-line.orange {
    color: orange;
  }

  .break-line.blue {
    color: blue;
  }

  .break-line.gray {
    color: gray;
  }

  .break-line {
    text-align: left;
    white-space: break-spaces;
  }

  .hr {
    border: none;
    border-top: 1px dotted #ccc;
    color: #fff;
    background-color: #fff;
    height: 1px;
    width: 50%;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }
</style>
