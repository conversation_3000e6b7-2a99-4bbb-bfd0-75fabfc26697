<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>商品情報表示設定管理</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> 画面選択 </CCol>
            <CCol sm="5">
              <CFormSelect
                name="page"
                :options="pageOptions"
                v-model="search_condition.window_id"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="3" class="form-inline">
              <CFormSelect
                name="language_code"
                :options="languageOptions"
                v-model="search_condition.language_code"
                class="form-group col-sm-3 pl-0"
                addInputClasses="w-100"
              />
            </CCol>
          </CRow>
        </CForm>

        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <span class="warn"
        >必ず保存ボタンをクリックし、変更内容を保存してください。</span
      >
    </CRow>
    <CRow>
      <CCol sm="12">
        <DisplayItemTable
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          :fieldList="fieldList"
          :windowId="search_condition.window_id"
          :displayArea="search_condition.display_area"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          caption="項目一覧"
          @add-new-item="addNewItem"
          @delete-item="deleteItem"
          @move-up="moveUp"
          @move-down="moveDown"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
          errorMsg = [];
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="errorMsg">
          <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            isErrorDialog = false;
            errorMsg = [];
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="confirmModal"
      @close="
        () => {
          confirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この内容で保存してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="confirmModal = false" color="dark">キャンセル</CButton>
        <CButton @click="saveDisplayItems" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader :closeButton="false">
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              compModal = false;
            }
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <!-- 固定保存ボタン -->
    <div class="fixed-save-button">
      <CButton
        color="primary"
        size="sm"
        @click="confirmModal = true"
        :disabled="loading"
      >
        <i class="fas fa-save"></i> 保存
      </CButton>
    </div>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {useCommonStore} from '@/store/common';
import {computed, onMounted, ref, watch} from 'vue';
import {useRouter} from 'vue-router';
import DisplayItemTable from '../../components/tenant/displayItem/DisplayItemTable.vue';

  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);
  // 検索条件
  const pageOptions = ref([]);
  const languageOptions = ref([]);

  // Screen params
  const resourceList = ref([]);
  const search_condition = ref({
    window_id: 'top',
    language_code: '',
    display_area: '',
  });

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // Error dialog
  const isErrorDialog = ref(false);
  const errorMsg = ref([]);

  // Confirm dialog
  const confirmModal = ref(false);

  // Completion dialog
  const compModal = ref(false);

  // 項目表示番号
  const fieldMappingNo = ref(null);

  // 選択言語
  const preLanguageCode = ref('ja');

  // 画面選択リスト
  const constPageOptions = ref([
    {value: 'top', label: 'TOPページ'},
    {value: 'items_list', label: '商品一覧（リスト）'},
    {value: 'items_panel', label: '商品一覧（パネル）'},
    {value: 'item_detail', label: '商品詳細'},
    {value: 'mypage_item', label: 'マイページ（商品）'},
    {value: 'mypage_member', label: 'マイページ（会員）'},
  ]);

  // 言語リスト
  const constLanguageOptions = ref([]);

  // 項目リスト
  const fieldList = ref([]);

  onMounted(() => {
    loading.value = true;
    getConstants()
      .then(() => {
        getResourceList()
          .then(postage => {
            resourceList.value = postage;
            loading.value = false;
          })
          .then(() => {
            // 全項目名の取得
            getFieldList()
              .then(() => {
                loading.value = false;
                preLanguageCode.value = search_condition.value.language_code;
              })
              .catch(error => {
                console.log(error);
                loading.value = false;
                errorMsg.value = Methods.parseHtmlResponseError(router, error);
                isErrorDialog.value = true;
              });
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            errorMsg.value = Methods.parseHtmlResponseError(router, error);
            isErrorDialog.value = true;
          });
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        isErrorDialog.value = true;
      });
  });

  watch(
    () => search_condition,
    newVal => {
      store.set(['resourceSearchCondition', newVal]);
    }
  );

  const totalCount = computed(() => {
    return Base.number2string(total_count.value);
  });

  const currentCount = computed(() => {
    return Base.number2string(current_count.value);
  });

  const getConstants = () => {
    const request = {
      key_strings: ['LANGUAGE_CODE'],
    };
    return Methods.apiExecute('get-constants-by-keys', request)
      .then(response => {
        if (response.status === 200) {
          // 言語
          constLanguageOptions.value = [];
          for (const constant of response.data) {
            switch (constant.key_string) {
              case 'LANGUAGE_CODE':
                constLanguageOptions.value.push({
                  value: constant.value1,
                  label: constant.value2,
                });
                break;
              default:
                break;
            }
          }
          return Methods.apiExecute('get-tenant-language-list', {}).then(response => {
            if (response.status === 200) {
              // 画面選択
              constPageOptions.value.forEach(element => {
                pageOptions.value.push({
                  value: element.value,
                  label: element.label,
                });
              });

              // 言語リスト
              languageOptions.value = response.data.language_code_list.map(cd => {
                const lang_list = constLanguageOptions.value.find(
                  item => item.value === cd
                );
                return {
                  value: cd,
                  label: lang_list ? lang_list.label : '',
                };
              });

              // 言語コードの初期値を設定（最初の言語を使用）
              if (languageOptions.value.length > 0) {
                search_condition.value.language_code = languageOptions.value[0].value;
                // storeも更新
                store.set([
                  'resourceSearchCondition',
                  {window_id: 'top', language_code: languageOptions.value[0].value, display_area: ''},
                ]);
              }
            }
            return Promise.resolve();
          });
        }
        return Promise.resolve();
      });
  };

  const search = () => {
    isErrorDialog.value = false;
    if (
      search_condition.value.window_id === '' &&
      search_condition.value.language_code === ''
    ) {
      errorMsg.value = ['1つ以上の条件を選択してください。'];
      isErrorDialog.value = true;
      return;
    }

    loading.value = true;
    getResourceList()
      .then(postage => {
        resourceList.value = postage;
        if (preLanguageCode.value !== search_condition.value.language_code) {
          getFieldList()
            .then(() => {
              preLanguageCode.value = search_condition.value.language_code;
              loading.value = false;
            })
            .catch(error => {
              console.log(error);
              loading.value = false;
              errorMsg.value = Methods.parseHtmlResponseError(router, error);
              isErrorDialog.value = true;
            });
        } else {
          loading.value = false;
        }
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        isErrorDialog.value = true;
      });
  };

  const getResourceList = () => {
    // Request to server
    return Methods.apiExecute(
      'get-display-field-list',
      search_condition.value
    ).then(response => {
      if (response.status === 200) {
        const resourceList = response.data.data;
        total_count.value = response.data ? response.data.total_count || 0 : 0;
        current_count.value = response.data
          ? response.data.current_count || 0
          : 0;
        // 項目表示番号の取得
        fieldMappingNo.value = response.data.data[0]
          ? response.data.data[0].field_mapping_no
          : null;
        return Promise.resolve(resourceList);
      }
      return Promise.resolve(null);
    });
  };

  const getFieldList = () => {
    // 全項目名の取得
    const param = {
      language_code: search_condition.value.language_code,
    };
    return Methods.apiExecute('get-field-all-list', param).then(response => {
      if (response.status === 200) {
        fieldList.value = response.data.data;
      }
      return Promise.resolve();
    });
  };

  const addNewItem = item => {
    if (resourceList.value.some(field => field.field_no === item.field_no)) {
      errorMsg.value = ['同じ項目は追加できません。'];
      isErrorDialog.value = true;
      return;
    }

    // Update the search condition with the display area from the item
    if (item.display_area && search_condition.value.window_id === 'item_detail') {
      search_condition.value.display_area = item.display_area;
    }

    // 新しい配列を作成してリアクティビティを確保
    resourceList.value = [
      ...resourceList.value,
      {
        ...item,
        id: resourceList.value.length + 1,
      },
    ];
  };

  const deleteItem = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index !== -1) {
      // 新しい配列を作成
      resourceList.value = resourceList.value.filter(
        field => field.field_no !== item.field_no
      );
    }
  };

  const moveUp = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index > 0) {
      // 新しい配列を作成
      const newList = [...resourceList.value];
      [newList[index - 1], newList[index]] = [
        newList[index],
        newList[index - 1],
      ];
      resourceList.value = newList;
    }
  };

  const moveDown = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index !== -1 && index < resourceList.value.length - 1) {
      // 新しい配列を作成
      const newList = [...resourceList.value];
      [newList[index], newList[index + 1]] = [
        newList[index + 1],
        newList[index],
      ];
      resourceList.value = newList;
    }
  };

  // DB変更処理
  const executeApiAndRefresh = async (apiName, params) => {
    loading.value = true;
    try {
      const response = await Methods.apiExecute(apiName, params);
      if (response.status === 200) {
        // 一覧の再取得
        const postage = await getResourceList();
        resourceList.value = postage;
        confirmModal.value = false;
        loading.value = false;
        compModal.value = true;
      }
    } catch (error) {
      console.log(error);
      confirmModal.value = false;
      loading.value = false;
      compModal.value = false;
      errorMsg.value = Methods.parseHtmlResponseError(router, error);
      isErrorDialog.value = true;
    }
  };

  const saveDisplayItems = async () => {
    if (resourceList.value.length === 0) {
      if (fieldMappingNo.value === null) {
        confirmModal.value = false;
        errorMsg.value = ['保存する項目がありません。'];
        isErrorDialog.value = true;
        return;
      } else {
        // 削除処理
        const params = {
          field_mapping_no: fieldMappingNo.value,
        };
        await executeApiAndRefresh('delete-display-field-list', params);
      }
    } else {
      // 登録・更新処理
      const field_no_list = resourceList.value.map(item => ({
        no: item.field_no,
        label: item.logical_name,
        physical_name: item.physical_name,
        display_area: item.display_area || search_condition.value.display_area,
      }));

      const params = {
        field_mapping_no: fieldMappingNo.value,
        window_id: search_condition.value.window_id,
        language_code: search_condition.value.language_code,
        field_no: field_no_list,
      };
      await executeApiAndRefresh('regist-display-field-list', params);
    }
  };
</script>

<style scoped>
  .fixed-save-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    border-radius: 50px;
  }

  .fixed-save-button button {
    border-radius: 50px;
    padding: 12px 24px;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .warn {
    color: #f00;
  }
</style>
