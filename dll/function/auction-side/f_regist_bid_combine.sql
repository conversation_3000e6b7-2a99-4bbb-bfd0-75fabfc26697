CREATE OR REPLACE FUNCTION public.f_regist_bid_combine
(
    in_bid_user_no bigint,
    in_user_name character varying,
    in_bid_items json
)
RETURNS json
LANGUAGE plpgsql

AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札をN件登録する（開催回を複数またがっていても良い）
-- Parameters
-- @param in_bid_user_no 利用者番号
-- @param in_user_name 使わない
-- @param in_bid_items 入札内容JSON（exhibitionItemNo:出品番号、bidPrice:入札金額、bidQuantity:入札数量
----------------------------------------------------------------------------------------------------

  error_code character varying = '';
  exhibition_rec record; -- 開催回情報

  -- 一次格納用
  temp_text text = '';
  temp_sql text = '';
  temp_exhibition_item_no character varying;
  temp_json json;

  -- 開催回種別情報
  exhibition_item_no bigint;
  ext_auction_classification integer;

  -- 返却用
  tmp_top_text text = '';
  top_changed_text text = '';
  top_2nd_text text = '';
  top_changed_count integer = 0;
  result_text text = '';
  result_data json;
  status integer = 200;

  -- エラー
  error_count integer = 0;

  BEGIN
  ----------------------------------------------------------------------------------------------------
  -- 入札対象データごとにループ処理
  ----------------------------------------------------------------------------------------------------
  FOR temp_json IN SELECT * FROM json_array_elements(in_bid_items)
  LOOP
    error_code := '';
    -- JSON形式で取得する
    temp_exhibition_item_no := temp_json ->> 'exhibitionItemNo';
    IF temp_exhibition_item_no IS NULL OR temp_exhibition_item_no = '' THEN
      error_code := 'EXHIBITION_ITEM_REQUIRED_ERROR';
    ELSEIF NOT temp_exhibition_item_no :: text ~ '^([1-9]\d*|0)$' THEN
      error_code := 'EXHIBITION_ITEM_FORMAT_ERROR';
    ELSE
      exhibition_item_no := replace(temp_exhibition_item_no, '"', '');

      ----------------------------------------------------------------------------------------------------
      -- 当該商品の開催回および出品の情報を取得する
      ----------------------------------------------------------------------------------------------------
      temp_sql := 'SELECT B.tenant_no
                        , B.exhibition_item_no
                        , C.exhibition_classification_info
                     FROM t_exhibition_item B
                          LEFT OUTER JOIN t_exhibition C
                                       ON B.exhibition_no = C.exhibition_no
                                      AND C.delete_flag = 0
                    WHERE B.exhibition_item_no = $1
                    ORDER BY B.create_datetime DESC
                    LIMIT 1';
      EXECUTE temp_sql INTO exhibition_rec USING exhibition_item_no;

      -- オークション方式(1:せり上げ、2:封印入札)
      ext_auction_classification := exhibition_rec.exhibition_classification_info->'auctionClassification';

      temp_text := '[{
        "exhibitionItemNo": "' || exhibition_item_no || '",
        "bidPrice": ' || (temp_json ->> 'bidPrice') || ',
        "bidQuantity":' || (temp_json ->> 'bidQuantity') || '
      }]';

      RAISE NOTICE 'Calling % on f_regist_bid with: user_no=%, user_name=%, payload=%',
        CASE WHEN ext_auction_classification=1 THEN 'f_regist_bid' ELSE 'f_regist_bid_sealed' END,
        in_bid_user_no,
        in_user_name,
        temp_text;

      IF ext_auction_classification = 1 THEN
        EXECUTE 'SELECT f_regist_bid FROM "f_regist_bid"($1,$2,$3)'
         INTO result_data
        USING
          in_bid_user_no,
          in_user_name,
          temp_text::json;
      ELSE
        EXECUTE 'SELECT f_regist_bid_sealed FROM "f_regist_bid_sealed"($1,$2,$3)'
          INTO result_data
          USING
            in_bid_user_no,
            in_user_name,
            temp_text::json;
      END IF;

    END IF;

    IF error_code <> '' THEN
      temp_text := '{"exhibition_item_no":"' || COALESCE(exhibition_item_no::character varying, temp_exhibition_item_no, '') || '", "errorMessage":"' || error_code || '"}';
      error_count := error_count + 1;
      status := 200;
    ELSE
      -- Concatenate bidList if not null
      IF result_data -> 'result' -> 'bidList' IS NOT NULL THEN
        SELECT array_to_string(array_agg(element), ', ')
        INTO temp_text
        FROM (
          SELECT json_array_elements_text(result_data -> 'result' -> 'bidList') AS element
        ) subquery;

        IF temp_text IS NOT NULL THEN
          IF result_text <> '' THEN
            result_text := COALESCE(result_text, '') || ',';
          END IF;
          result_text := COALESCE(result_text, '') || temp_text;
        END IF;
      END IF;

      -- Concatenate topChangeList if not null
      IF result_data -> 'result' -> 'topChangeList' IS NOT NULL THEN
        SELECT array_to_string(array_agg(element), ', ')
        INTO temp_text
        FROM (
          SELECT json_array_elements_text(result_data -> 'result' -> 'topChangeList') AS element
        ) subquery;

        IF temp_text IS NOT NULL THEN
          IF top_changed_text <> '' THEN
            top_changed_text := COALESCE(top_changed_text, '') || ', ';
          END IF;
          top_changed_text := COALESCE(top_changed_text, '') || temp_text;
        END IF;
      END IF;

      -- Concatenate topAndSecondList if not null
      IF result_data -> 'result' -> 'topAndSecondList' IS NOT NULL THEN
        SELECT array_to_string(array_agg(element), ', ')
        INTO temp_text
        FROM (
          SELECT json_array_elements_text(result_data -> 'result' -> 'topAndSecondList') AS element
        ) subquery;

        IF temp_text IS NOT NULL THEN
          IF top_2nd_text <> '' THEN
            top_2nd_text := COALESCE(top_2nd_text, '') || ', ';
          END IF;
          top_2nd_text := COALESCE(top_2nd_text, '') || temp_text;
        END IF;
      END IF;


      -- Update counts
      top_changed_count := top_changed_count + (result_data -> 'result' ->> 'topChangeCount')::integer;
      error_count := error_count + (result_data -> 'result' ->> 'errorCount')::integer;
      status := result_data -> 'result' ->> 'statusCode';

    END IF;
  END LOOP;


  ----------------------------------------------------------------------------------------------------
  -- Final result
  ----------------------------------------------------------------------------------------------------
  result_data := jsonb_build_object(
    'result', jsonb_build_object(
      'statusCode', status,
      'errorCount', error_count,
      'topChangeCount', top_changed_count,
      'bidList', ('[' || COALESCE(result_text, '') || ']')::json,
      'topChangeList', ('[' || top_changed_text || ']')::json,
      'topAndSecondList', ('[' || top_2nd_text || ']')::json
    )
  );

  RETURN result_data;

END;

$BODY$;
