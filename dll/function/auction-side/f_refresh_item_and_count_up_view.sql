CREATE OR REPLACE FUNCTION public.f_refresh_item_and_count_up_view(
    in_exhibition_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying,
    in_member_no bigint
  )
  RETURNS TABLE (
    exhibition_item_no bigint,
    exhibition_no bigint,
    exhibition_name character varying,
    preview_end_datetime timestamp with time zone,
    auction_classification integer,
    item_no bigint,
    view_count integer,
    free_fields jsonb,
    mapping_free_fields jsonb,
    bid_histories jsonb,
    images json [],
    pdfs character varying [],
    bid_status jsonb,
    attention_info jsonb,
    is_favorited boolean,
    category_code character varying,
    category character varying,
    sold_out integer
  ) LANGUAGE 'plpgsql' COST 100 VOLATILE PARALLEL UNSAFE ROWS 1000 AS $BODY$
DECLARE ----------------------------------------------------------------------------------------------------
  -- 入札会商品取得
  ----------------------------------------------------------------------------------------------------
  BEGIN RETURN QUERY WITH
  -- 商品詳細のURLは「/details/exhibition_item_no」で対応
  findExhibitionByManageNo as (
    SELECT
      TE.exhibition_no,
      COALESCE(TE.preview_end_datetime, TE.end_datetime) as preview_end_datetime,
      TEI. exhibition_item_no,
      TEL.exhibition_name,
      CAST(TE.exhibition_classification_info->>'auctionClassification' AS integer) AS auction_classification
    FROM t_item TI
    LEFT JOIN t_lot_detail TLD ON TI.item_no =  TLD.item_no
    LEFT JOIN t_exhibition_item TEI
           ON TLD.lot_no = TEI.lot_no
          AND TEI.tenant_no = in_tenant_no
          AND TEI.exhibition_item_no = in_exhibition_item_no
    LEFT JOIN t_exhibition TE ON TE.exhibition_no = TEI.exhibition_no
    LEFT JOIN t_exhibition_localized TEL
           ON TE.exhibition_no = TEL.exhibition_no
          AND TEL.language_code = in_lang_code
    WHERE TEI.exhibition_item_no = in_exhibition_item_no
    ORDER BY TEI.create_datetime DESC    -- 登録日が最も新しいものが対象となる
    LIMIT 1
  ),
  item AS (
    UPDATE t_exhibition_item TEI
    SET view_count = COALESCE(TEI.view_count, 0) + 1
    FROM findExhibitionByManageNo FEMN
    WHERE TEI.exhibition_item_no = FEMN.exhibition_item_no
    RETURNING TEI.exhibition_item_no
  ),
  get_favorite AS (
    SELECT IT.exhibition_item_no,
      SUM(
        CASE
          WHEN TEIF.favorite_no IS NOT NULL THEN 1
          ELSE 0
        END
      ) AS fav_count
    FROM item IT
      LEFT JOIN t_exhibition_item_favorite TEIF ON TEIF.exhibition_item_no = IT.exhibition_item_no
      AND TEIF.tenant_no = in_tenant_no
      AND TEIF.member_no = in_member_no
    WHERE in_member_no IS NOT NULL
    GROUP BY IT.exhibition_item_no
  ),
  bid_history AS (
    SELECT TBH.exhibition_item_no,
      jsonb_agg(
        jsonb_build_object(
          'nickname',
          TBH.bid_nickname,
          -- 入札時点のニックネーム
          'bid_price',
          TBH.bid_price,
          'after_current_price',
          TBH.after_current_price,
          'create_datetime',
          TBH.create_datetime
        )
        ORDER BY TBH.bid_price DESC,
          TBH.create_datetime ASC
      ) AS histories
    FROM (
        SELECT *
        FROM (
            SELECT TBH.exhibition_item_no,
              TBH.bid_nickname,
              TBH.bid_price,
              TBH.after_current_price,
              TBH.create_datetime,
              ROW_NUMBER() OVER (
                PARTITION BY TBH.member_no
                ORDER BY TBH.create_datetime DESC
              ) AS row_number
            FROM t_bid_history TBH
            CROSS JOIN item
            LEFT JOIN t_bid TB
              ON TB.exhibition_item_no = TBH.exhibition_item_no
              AND TB.member_no = TBH.member_no
            WHERE TBH.exhibition_item_no = item.exhibition_item_no
              AND TB.bid_price IS NOT NULL
              AND TB.bid_price <> 0
            ORDER BY TBH.member_no,
              TBH.create_datetime DESC
          ) BH
        WHERE BH.row_number = 1
      ) TBH
    GROUP BY TBH.exhibition_item_no
  ),
  bid_status AS (
    SELECT FEMN.exhibition_item_no AS exhibition_item_no,
      BSTT.result->'attention_info' AS attention_info,
      BSTT.result->'bid_status' AS bid_status
      FROM findExhibitionByManageNo FEMN
  CROSS JOIN LATERAL f_get_auction_item_bid_status(
      ARRAY[FEMN.exhibition_item_no],
      in_tenant_no,
      in_member_no
    ) BSTT
),
  item_info AS (
    SELECT TEI.exhibition_item_no,
      TEI.view_count,
      jsonb_agg(DISTINCT TIL.free_field) AS free_fields,
      TIL.free_field->>'commission'  AS commission,
      TIL.item_no,
      TEI.hummer_flag AS sold_out,
      array_agg(
        json_build_object(
          'file_path',
          TIAF.file_path,
          'postar_file_path',
          TIAF.postar_file_path
        )
        ORDER BY CASE
            WHEN TIAF.postar_file_path IS NULL THEN TIAF.serial_number
            ELSE (1000 + TIAF.serial_number)
          END
      ) FILTER (
        WHERE TIAF.division = 1
      ) AS images,
      array_agg(TIAF.file_path) FILTER (
        WHERE TIAF.division = 2
      ) AS pdfs
    FROM t_exhibition_item TEI
      JOIN t_lot TL ON TL.lot_no = TEI.lot_no
      AND TL.tenant_no = in_tenant_no
      AND (
        TL.delete_flag IS NULL
        OR TL.delete_flag = 0
      )
      JOIN t_lot_detail TLD ON TLD.lot_no = TEI.lot_no
      AND TLD.tenant_no = in_tenant_no
      JOIN t_item TI ON TLD.item_no = TI.item_no
      AND TI.tenant_no = in_tenant_no
      AND (
        TI.delete_flag IS NULL
        OR TI.delete_flag = 0
      )
      JOIN t_item_localized TIL ON TIL.item_no = TI.item_no
      AND TIL.tenant_no = in_tenant_no
      AND TIL.language_code = in_lang_code
      AND (
        TIL.delete_flag IS NULL
        OR TIL.delete_flag = 0
      )
      LEFT JOIN t_item_ancillary_file TIAF ON TIAF.manage_no = TI.manage_no
      AND TIAF.tenant_no = in_tenant_no
      AND TIAF.item_no = TI.item_no
      AND (
        TIAF.language_code = in_lang_code
        OR TIAF.language_code = 'common'
      )
      AND TIAF.delete_flag = 0
      CROSS JOIN item
    WHERE (
        TEI.delete_flag IS NULL
        OR TEI.delete_flag = 0
      )
      AND TEI.exhibition_item_no = item.exhibition_item_no
    GROUP BY TEI.exhibition_item_no, TIL.item_no, commission
  ),
  product_category AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(mcl.value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = in_lang_code
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
  ),
  field_mapping AS (
    SELECT jsonb_agg(
      jsonb_build_object(
        'no', (field_obj->>'no')::bigint,
        'physical_name', field_obj->>'physical_name',
        'label', field_obj->>'label',
        'display_area', field_obj->>'display_area'
      )
      ORDER BY field_index
    ) AS mapping_fields
    FROM m_field_mapping mfm
    CROSS JOIN LATERAL jsonb_array_elements(mfm.field_no) WITH ORDINALITY AS t(field_obj, field_index)
    WHERE mfm.window_id = 'item_detail'
      AND mfm.language_code = in_lang_code
      AND mfm.tenant_no = in_tenant_no
      AND (mfm.delete_flag IS NULL OR mfm.delete_flag = 0)
  ),
  combined_categories AS (
    SELECT * FROM (
      SELECT category_codes.item_no,
            product_category.value1 AS category_code,
            product_category.value2,
            ROW_NUMBER() OVER (PARTITION BY category_codes.item_no ORDER BY category_codes.category_code) AS row_number
        FROM (
          SELECT TIL1.item_no, jsonb_array_elements(TIL1.free_field->'categories')->>'category_code' AS category_code
          FROM t_item_localized TIL1
        UNION
          SELECT  TIL2.item_no,TIL2.free_field->'basic_category'->>'category_code' AS category_code
          FROM t_item_localized TIL2
        ) AS category_codes
        LEFT JOIN product_category
              ON category_codes.category_code = product_category.value3
        WHERE product_category.value2 IS NOT NULL
    ) CC
    WHERE CC.row_number = 1
  )

SELECT IT.exhibition_item_no,
  TE.exhibition_no,
  TE.exhibition_name,
  TE.preview_end_datetime,
  TE.auction_classification,
  II.item_no,
  II.view_count,
  II.free_fields,
  FM.mapping_fields AS mapping_free_fields,
  BH.histories,
  II.images,
  II.pdfs,
  BSTT.bid_status,
  BSTT.attention_info,
  (
    CASE
      WHEN GF.fav_count > 0 THEN TRUE
      ELSE FALSE
    END
  ) AS is_favorited,
  (
    CASE
      WHEN CC.category_code IS NOT NULL THEN CC.category_code
      ELSE 'ct11'
    END
  ) AS category_code,
  (
    CASE
      WHEN CC.value2 IS NOT NULL THEN CC.value2
      ELSE (SELECT PC.value2 FROM product_category PC WHERE PC.value1 = 'ct11')
    END
  ) AS category,
  II.sold_out
FROM item IT
  LEFT JOIN findExhibitionByManageNo TE ON TE.exhibition_item_no = TE.exhibition_item_no
  LEFT JOIN bid_history BH ON BH.exhibition_item_no = IT.exhibition_item_no
  LEFT JOIN item_info II ON II.exhibition_item_no = IT.exhibition_item_no
  LEFT JOIN get_favorite GF ON GF.exhibition_item_no = IT.exhibition_item_no
  LEFT JOIN bid_status BSTT ON BSTT.exhibition_item_no = IT.exhibition_item_no
  LEFT JOIN combined_categories CC ON CC.item_no = II.item_no
  CROSS JOIN field_mapping FM
WHERE IT.exhibition_item_no IS NOT NULL
ORDER BY IT.exhibition_item_no;
END;
$BODY$;
