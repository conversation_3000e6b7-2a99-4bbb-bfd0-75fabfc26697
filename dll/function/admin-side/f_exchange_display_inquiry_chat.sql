CREATE OR REPLACE FUNCTION public.f_exchange_display_inquiry_chat (
    in_tenant_no bigint,
    in_exhibition_message_no bigint,
    in_hidden_flag bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 問合せチャットのメッッセージの表示・非表示を変更
-- Parameters
-- @param in_tenant_no
-- @param in_exhibition_message_no
-- @param in_hidden_flag
----------------------------------------------------------------------------------------------------

BEGIN

  UPDATE t_exhibition_message
     SET hidden_flag = in_hidden_flag
   WHERE exhibition_message_no = in_exhibition_message_no
     AND tenant_no = in_tenant_no;

  result := true;
  status := 200;
  message := '';

  RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
