CREATE OR REPLACE FUNCTION public.f_upsert_item(
    in_stock_data jsonb,
    in_tenant_no bigint,
    in_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql
COST 100
VOLATILE
AS $BODY$
/************************************************************************/
--  処理内容： 在庫データ連携
--  引数   : in_stock_data データ(json形)
--  引数   : in_tenant_no　テナント番号
--  引数   : in_admin_no 管理者
/************************************************************************/
DECLARE
  return_item_no bigint;

BEGIN

  IF in_stock_data->>'item_no' IS NOT NULL THEN
    -- Update exist data
    WITH
    exist_data AS (
     UPDATE t_item TI
        SET  tenant_no                = in_tenant_no,
            recommend_start_datetime = CASE WHEN in_stock_data->>'recommend_start_datetime' IS NULL THEN null
                                          -- ELSE to_timestamp(in_stock_data->>'recommend_start_datetime','yyyy/MM/dd HH24:MI:SS')
                                          ELSE (in_stock_data->>'recommend_start_datetime')::timestamptz
                                        END,
            recommend_end_datetime   = CASE WHEN in_stock_data->>'recommend_start_datetime' IS NULL THEN null
                                          ELSE (in_stock_data->>'recommend_start_datetime')::timestamptz
                                          -- ELSE to_timestamp(in_stock_data->>'recommend_start_datetime','yyyy/MM/dd HH24:MI:SS')
                                        END,
            update_admin_no          = in_admin_no,
            update_datetime          = now()
      WHERE in_stock_data IS NOT NULL
        AND TI.item_no = (in_stock_data->>'item_no')::bigint
        -- AND TI.linked_flag = 0 再出品許容のため条件削除
        AND TI.delete_flag = 0
      RETURNING
            TI.item_no,
            TI.manage_no,
            TI.status
    ),
    update_exhibition_item AS (
       UPDATE t_exhibition_item TEI
          SET
              default_end_datetime = (
              CASE WHEN in_stock_data->>'default_end_datetime' IS NULL THEN default_end_datetime
                    ELSE to_timestamp(in_stock_data->>'default_end_datetime','yyyy/MM/dd HH24:MI:SS')
              END),
              cancel_flag     = 0, -- 同一商品の同一入札会への再出品を許容
              change_flag     = 1,
              update_admin_no = in_admin_no,
              update_datetime = now()
         FROM t_lot_detail TLD
         JOIN exist_data ED
           ON TLD.item_no = ED.item_no
          AND ED.status = 2
        WHERE TLD.lot_no = TEI.lot_no
          AND ( -- 再出品時、同じ商品管理番号の商品を更新しない条件を追加
            (in_stock_data->>'lot_no' IS NOT NULL) AND
            (in_stock_data->>'lot_no' = TEI.lot_no::text)
          )
          AND TEI.end_datetime > now() -- 再出品の商品更新時、流札後の入札会を更新しない条件を追加
        RETURNING
              TEI.exhibition_item_no
    ),
    all_localized AS (
      SELECT in_tenant_no AS tenant_no,
             ED.item_no,
             localized.language_code,
             localized.field_map AS free_field
        FROM exist_data ED
        CROSS JOIN LATERAL jsonb_array_elements(in_stock_data->'localized_json_array') AS elem
        CROSS JOIN LATERAL jsonb_to_record(elem) AS localized(language_code text, field_map jsonb)
    ),
    exist_localized AS (
      UPDATE t_item_localized TIL
         SET free_field      = (COALESCE(TIL.free_field, '{}'::jsonb) || AL.free_field),
             update_admin_no = in_admin_no,
             update_datetime = now()
        FROM all_localized AL
       WHERE TIL.tenant_no = AL.tenant_no
         AND TIL.item_no = AL.item_no
         AND TIL.language_code = AL.language_code
       RETURNING
             TIL.item_no,
             TIL.language_code
    ),
    new_localized AS (
      INSERT INTO t_item_localized (
        tenant_no,
        item_no,
        language_code,
        free_field,
        create_admin_no,
        update_admin_no,
        create_datetime,
        update_datetime
      )
      SELECT
        AL.tenant_no,
        AL.item_no,
        AL.language_code,
        AL.free_field,
        in_admin_no,
        in_admin_no,
        now(),
        now()
      FROM all_localized AL
      WHERE NOT EXISTS (
        SELECT 1 FROM t_item_localized TIL
        WHERE TIL.tenant_no = AL.tenant_no
          AND TIL.item_no = AL.item_no
          AND TIL.language_code = AL.language_code
      )
      RETURNING
        item_no,
        language_code
    ),
    combined_localized AS (
      SELECT item_no, language_code FROM exist_localized
      UNION ALL
      SELECT item_no, language_code FROM new_localized
    )

    SELECT item_no INTO return_item_no FROM combined_localized LIMIT 1;

  ELSE
    -- Insert new data
    WITH get_item_no AS (
      SELECT nextval('t_item_item_no_seq'::regclass) as item_no
    ),
    new_data AS (
      -- Insert new item (manage_noを指定しない場合はitem_noを設定する)
      INSERT INTO t_item (
        tenant_no,
        item_no,
        manage_no,
        status,
        price_display_flag,
        area_id,
        image,
        recommend_start_datetime,
        recommend_end_datetime,
        create_admin_no,
        update_admin_no

      )
      VALUES (
        in_tenant_no,
        (SELECT item_no FROM get_item_no),
        (CASE WHEN in_stock_data->>'manage_no' IS NOT NULL
                THEN ((in_stock_data->>'manage_no')::character varying)
                ELSE (SELECT item_no::character varying FROM get_item_no)
        END),
        0,
        0,
        null,
        '{}'::text[],
        CASE WHEN in_stock_data->>'recommend_start_datetime' IS NULL THEN null
              ELSE to_timestamp(in_stock_data->>'recommend_start_datetime','yyyy/MM/dd HH24:MI:SS')
        END,
        CASE WHEN in_stock_data->>'recommend_end_datetime' IS NULL THEN null
              ELSE to_timestamp(in_stock_data->>'recommend_end_datetime','yyyy/MM/dd HH24:MI:SS')
        END,
        in_admin_no,
        in_admin_no
      )
      RETURNING
          t_item.item_no,
          t_item.manage_no,
          t_item.status
    ),
    -- insert_item_ancillary_file AS (
    --    INSERT INTO t_item_ancillary_file (
    --     tenant_no,
    --     item_no,
    --     manage_no,
    --     language_code,
    --     division,
    --     serial_number,
    --     file_path,
    --     postar_file_path
    --   )
    --   VALUES (
    --     in_tenant_no,
    --     (SELECT item_no FROM new_data),
    --     (SELECT manage_no FROM new_data),
    --     'common',
    --     1,
    --     1,
    --     in_stock_data.file_path,
    --     null
    --   )
    --   RETURNING
    --     TIAF.item_ancillary_file_no
    -- ),
    prepare_localized AS (
      SELECT in_tenant_no AS tenant_no,
             TI.item_no,
             localized.language_code,
             localized.field_map AS free_field,
             in_admin_no AS create_admin_no,
             in_admin_no AS update_admin_no
        FROM get_item_no TI
        CROSS JOIN LATERAL jsonb_array_elements(in_stock_data->'localized_json_array') AS elem
        CROSS JOIN LATERAL jsonb_to_record(elem) AS localized(language_code text, field_map jsonb)
    ),
    new_localized AS (
      INSERT INTO t_item_localized (
        tenant_no,
        item_no,
        language_code,
        free_field,
        create_admin_no,
        update_admin_no
      )
      (
        SELECT * FROM prepare_localized
      )
      RETURNING
        item_no, item_localized_no
    )

    SELECT item_no INTO return_item_no FROM new_localized;

  END IF;

  RETURN return_item_no;

END;
$BODY$;
