CREATE OR REPLACE FUNCTION public.f_get_exhibition_summary(
    in_tenant_no bigint,
    in_exhibition_no_array bigint[]
)
RETURNS TABLE(
    exhibition_no bigint,
    tenant_no bigint,
    category_id integer,
    status text,
    bid_count integer,
    exhibition_item_count integer,
    currency_id character varying,
    preview_start_datetime timestamp with time zone
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札会情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo

----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  SELECT
      A.exhibition_no
      , A.tenant_no
      , A.category_id
      , CASE A.status WHEN 0 THEN '落札結果未確定'
                      WHEN 1 THEN '落札結果確定済'
                      WHEN 2 THEN 'インボイス作成済'
                      WHEN 3 THEN 'インボイス仮完了'
                      WHEN 4 THEN 'インボイス完了済'
                      ELSE ''
        END
      , S.bid_count
      , S.exhibition_item_count
      , A.currency_id
      , A.preview_start_datetime
  FROM t_exhibition A
  LEFT JOIN m_tenant T
    ON T.tenant_no = A.tenant_no
  LEFT JOIN t_exhibition_summary S
    ON S.exhibition_no = A.exhibition_no
  WHERE
    A.exhibition_no = ANY(in_exhibition_no_array)
    AND A.tenant_no = in_tenant_no
    AND A.delete_flag = 0
    AND T.delete_flag = 0
  ORDER BY exhibition_no;

END;

$BODY$;
