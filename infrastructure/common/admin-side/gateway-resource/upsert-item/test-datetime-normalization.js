/**
 * Test script to verify datetime normalization functionality
 * Run this to ensure the datetime parsing logic works correctly
 */

// Helper function to normalize datetime string to 'YYYY/MM/DD HH24:MI:SS' format
const normalizeDatetime = (datetimeString) => {
  if (!datetimeString) return null
  
  try {
    // Parse the datetime string as Date object
    let date
    
    // Handle ISO 8601 format (e.g., '2025-08-05T08:22:00.000Z')
    if (datetimeString.includes('T')) {
      date = new Date(datetimeString)
    }
    // Handle 'YYYY-MM-DD HH:MM' format (missing seconds)
    else if (datetimeString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/)) {
      date = new Date(datetimeString + ':00')
    }
    // Handle complete 'YYYY-MM-DD HH:MM:SS' format
    else if (datetimeString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
      date = new Date(datetimeString)
    }
    // Handle 'YYYY/MM/DD HH:MM:SS' format (already correct)
    else if (datetimeString.match(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/)) {
      return datetimeString
    }
    // Try direct parsing as fallback
    else {
      date = new Date(datetimeString)
    }
    
    // Validate the parsed date
    if (isNaN(date.getTime())) {
      console.warn(`Invalid datetime string: ${datetimeString}`)
      return null
    }
    
    // Format as 'YYYY/MM/DD HH24:MI:SS'
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
    
  } catch (error) {
    console.error(`Error normalizing datetime: ${datetimeString}`, error)
    return null
  }
}

// Test cases
const testCases = [
  // Original problematic formats
  '2025-08-05T08:22:00.000Z',  // ISO 8601 format
  '2025-08-12 17:22',          // Missing seconds
  
  // Additional test cases
  '2025-08-12 17:22:30',       // Complete format
  '2025/08/12 17:22:30',       // Already correct format
  '2025-01-01T00:00:00Z',      // ISO without milliseconds
  '2025-12-31 23:59:59',       // Edge case
  '',                          // Empty string
  null,                        // Null value
  'invalid-date',              // Invalid format
]

console.log('Testing datetime normalization...\n')

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}:`)
  console.log(`  Input:  "${testCase}"`)
  console.log(`  Output: "${normalizeDatetime(testCase)}"`)
  console.log()
})

console.log('✅ Test completed! All formats should be normalized to YYYY/MM/DD HH:MM:SS format.')
