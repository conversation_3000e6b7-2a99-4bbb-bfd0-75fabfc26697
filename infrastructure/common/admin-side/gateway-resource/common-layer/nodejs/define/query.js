/**
 * ログインチェック用にユーザー情報を取得する
 * @param $1 利用者ID
 */
const GET_USER_FUNCTION = 'SELECT * FROM "f_get_user"($1);'

/**
 * ログインチェック用に管理者情報を取得する
 * @param $1 利用者ID
 */
const GET_ADMIN_FOR_LOGIN_FUNCTION =
  'SELECT * FROM "f_get_admin_for_login"($1,$2);'

/**
 * お知らせ一覧を取得する
 * @param $1 テナントID
 * @param $2 表示先コード
 * @param $3 表示開始日時
 * @param $4 表示終了日時
 */
const GET_NOTICE_LIST_FUNCTION =
  'SELECT * FROM "f_get_notice_list"($1, $2, $3, $4);'

/**
 * お知らせ番号経由でお知らせ情報を取得する
 * @param $1 テナント番号
 * @param $2 お知らせ番号
 */
const GET_NOTICE_BY_NOTICE_NO_FUNCTION =
  'SELECT * FROM "f_get_notice_by_notice_no"($1,$2);'

/**
 * お知らせを更新・登録する
 * @param $1 お知らせ番号
 * @param $2 テナント番号
 * @param $3 表示先コード
 * @param $4 言語コード
 * @param $5 表示開始日時
 * @param $6 表示終了日時
 * @param $7 タイトル
 * @param $8 タイトル
 * @param $9 サブタイトル
 * @param $10 本文
 * @param $11 リンク先URL
 * @param $12 添付ファイルURL
 * @param $13 作成者番号
 * @param $14 更新者番号
 */
const INSERT_OR_UPDATE_NOTICE_FUNCTION =
  'SELECT * FROM "f_insert_or_update_notice"($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14);'

/**
 * お知らせを削除する
 * @param $1 お知らせ番号
 * @param $2 更新者番号
 */
const DELETE_NOTICE_FUNCTION = 'SELECT * FROM "f_delete_notice"($1, $2);'

/**
 * お知らせの添付ファイルを取得する
 * @param $1 お知らせ番号
 */
const GET_NOTICE_FILES_FUNCTION = 'SELECT * FROM "f_get_notice_files"($1);'

/**
 * 定数一覧画面用に情報を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_key_string 定数キーワード
 * @param $3 in_get_mente_impossible_data_flag 編集不可フラグ（true:編集不可項目を取得、false:編集可能項目を取得）
 */
const GET_CONSTANT_LIST_FUNCTION =
  'SELECT * FROM "f_get_constant_list"($1,$2,$3);'

/**
 * 定数一覧画面用に情報を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_constan_no bigint 定数番号
 */
const GET_CONSTANTS_LANGUAGE_LIST_FUNCTION =
  'SELECT * FROM "f_get_constants_language_list"($1,$2);'

/**
 * 定数申込みを編集する
 * @param $1 in_constant_no bigint 定数番号
 * @param $2 in_tenant_no bigint テナントID
 * @param $3 in_key_string character varying 定数キー
 * @param $4 in_value_name character varying 名前
 * @param $5 in_sort_order integer 表示順
 * @param $6 in_description character varying 説明
 * @param $7 in_start_datetime timestamp with time zone 適用開始日時
 * @param $8 in_end_datetime timestamp with time zone 適用終了日時
 * @param $9 in_create_admin_no bigint 作成管理者番号
 * @param $10 in_chilConstants JSON[] 多言語の定数マスタ
 */
const REGIST_UPDATE_CONSTANTS_FUNCTION =
  'SELECT * FROM "f_regist_update_constant"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10);'

/**
 * 定数マスタ(多言語)を削除する
 * @param $1 in_constant_no bigint varying 定数番号
 * @param $2 in_tenant_no bigint テナントID
 */
const DELETE_CONSTANT_LOCALIZED_FUNCTION =
  'SELECT * FROM "f_delete_constant_localized"($1,$2);'

/**
 * 定数マスタを削除する
 * @param $1 in_constant_no bigint varying 定数番号
 */
const DELETE_CONSTANT_FUNCTION = 'SELECT * FROM "f_delete_constant"($1);'

/**
 * 入札会情報を取得する
 * @param $1 入札会No
 * @param $2 テナントNo
 * @param $3 開催地区ID
 * @param $4 下見開始日時(from)
 * @param $5 下見開始日時(to)
 * @param $6 開催回開始日時(from)
 * @param $7 開催回開始日時(to)
 * @param $8 入札会カテゴリ
 */
const GET_EXHIBITIONS =
  'SELECT * FROM "f_get_exhibitions"($1,$2,$3,$4,$5,$6,$7,$8);'

/**
 * 会員検索
 * @param $1 テナントNo
 * @param $2 検索キーワード
 */
const SEARCH_MEMBER_BY_NAME =
  'SELECT * FROM "f_search_member_by_name"($1,$2);'

/**
 * 開催回参加会員追加
 * @param $1 テナントNo
 * @param $2 検索キーワード
 */
const REGIST_EXHIBITION_MEMBERS =
  'SELECT * FROM "f_regist_exhibition_members"($1,$2,$3,$4);'

/**
 * 入札会情報を登録・更新する
 * @param $1 開催回番号
 * @param $2 テナント番号
 * @param $3 カテゴリ
 * @param $4 入札単位区分
 * @param $5 終了区分
 * @param $6 データJSON
 * @param $7 下見開始日時
 * @param $8 下見終了日時
 * @param $9 開催回開始日時
 * @param $10 開催回終了日時
 * @param $11 最大延長時刻
 * @param $12 延長開始判定分数
 * @param $13 延長加算分数
 * @param $14 ピッチ幅
 * @param $15 あと少し表示ピッチ
 * @param $16 開催回設定情報
 * @param $17 参加制限設定
 * @param $18 管理者番号
 * @returns result
 * @returns status
 * @returns message
 * @returns localize_error
 */
const UPSERT_EXHIBITIONS =
  'SELECT * FROM "f_upsert_exhibitions"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18);'

/**
 * 入札会情報を削除する
 * @param $1 テナント番号
 * @param $2 開催回番号
 * @param $3 管理者番号
 */
const DELETE_EXHIBITIONS = 'SELECT * FROM "f_delete_exhibitions"($1,$2,$3);'

/**
 * 出品情報の開始・完了時間を更新する
 * @param $1 開催回番号
 * @param $2 開始日時
 * @param $3 終了日時
 * @param $4 管理者番号
 * @returns return_exhibition_item_no 出品番号
 */
const UPDATE_EXHIBITION_ITEM_DATETIME =
  'SELECT * FROM "f_update_exhibition_item_datetime"($1,$2,$3,$4);'

/**
 * ロットNoの頭文字を更新する
 * @param $1 テナント番号
 * @param $2 開催回番号
 * @param $3 新prefix
 * @param $4 管理者番号
 * @returns return_exhibition_no 入札会番号
 */
const UPDATE_LOT_ID_PREFIX_FUNCTION =
  'SELECT * FROM "f_update_lot_id_prefix"($1,$2,$3,$4);'

/**
 * 出品情報を停止する
 * @param $1 テナント番号
 * @param $2 開催回番号
 * @param $3 ロットID
 * @param $4 管理者番号
 */
const CANCEL_EXHIBITION_ITEM =
  'SELECT * FROM "f_cancel_exhibition_item"($1,$2,$3,$4);'

/**
 * 出品停止を取り消す
 * @param $1 テナント番号
 * @param $2 開催回番号
 * @param $3 ロットID
 * @param $4 管理者番号
 */
const REPUBLISH_EXHIBITION_ITEM =
  'SELECT * FROM "f_republish_exhibition_item"($1,$2,$3,$4);'

/**
 * 商品情報を取得する
 * @param $1 ステータス
 * @param $2 新着マーク
 * @param $3 管理番号(from)
 * @param $4 管理番号(to)
 * @param $5 Serial(from)
 * @param $6 Serial(to)
 * @param $7 地区ID
 * @param $8 カテゴリー
 * @param $9 メーカー
 * @param $10 テナント番号
 * @param $11 言語コード
 */
const GET_ITEMS =
  'SELECT * FROM "f_get_items"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11);'

/**
 * 商品情報を取得する
 * @param $1 商品番号
 * @param $2 テナント番号
 * @param $3 言語コード
 */
const GET_ITEM_DETAIL = 'SELECT * FROM "f_get_item_detail"($1,$2,$3);'

/**
 * 商品のおすすめフラグを更新する
 * @param $1 商品番号配列
 * @param $2 おすすめフラグ配列
 * @param $3 テナント番号
 * @param $4 管理者番号
 * @param $5 おすすめ表示期間
 */
const UPDATE_ITEMS_RECOMMEND =
  'SELECT * FROM "f_update_items_recommend"($1,$2,$3,$4,$5);'

/**
 * キー項目に基づき定数情報を取得する
 * @param $1 定数キー配列
 * @param $2 テナントNo
 * @param $3 管理者利用言語コード
 */
const GET_CONSTANTS_BY_KEYS =
  'SELECT * FROM "f_get_constants_by_keys"($1,$2,$3);'

/**
 * アクセスチェック用のIPアドレス一覧を取得する
 * @param $1 テナント番号
 */
const GET_IP_ADDRESS_FOR_ACCESS_CHECK =
  'SELECT * FROM "f_get_ip_address_for_access_check"($1);'

/**
 * 管理者一覧を取得する
 * @param $1 テナント番号
 */
const GET_ADMIN_LIST_FUNCTION = 'SELECT * FROM "f_get_admin_list"($1);'

/**
 * 管理者データを取得する
 * @param $1 管理者番号
 */
const GET_ADMIN_BY_ADMIN_NO_FUNCTION =
  'SELECT * FROM "f_get_admin_by_admin_no"($1);'

/**
 * ログインIDで管理者データを取得する
 * @param $1 管理者番号
 * @param $2 テナント番号
 */
const GET_ADMIN_BY_LOGIN_ID_FUNCTION =
  'SELECT * FROM "f_get_admin_by_login_id"($1,$2);'

/**
 * 管理者データを登録・編集する
 * @param $1 テナント番号
 * @param $2 管理者名
 * @param $3 ログインID
 * @param $4 パスワード
 * @param $5 権限ID
 * @param $6 登録管理者番号
 * @param $7 編集管理者番号
 * @param $8 削除フラグ
 */
const INSERT_OR_UPDATE_ADMIN_FUNCTION =
  'SELECT * FROM "f_insert_or_update_admin"($1,$2,$3,$4,$5,$6,$7,$8);'

/**
 * 不達メールを取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_to_address character varying 宛先
 * @param $3 in_subject character varying タイトル
 * @param $4 in_start_datetime timestamp with time zone 適用開始日時
 * @param $5 in_end_datetime timestamp with time zone 適用終了日時
 */
const GET_EMAIL_NOTIFICATION_LIST_FUNCTION =
  'SELECT * FROM "f_get_email_notification_list"($1,$2,$3,$4,$5);'

/**
 * 不達メールの会員情報を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_to_address テナントID
 */
const GET_USER_BOUNCES_LIST_FUNCTION =
  'SELECT * FROM "f_get_user_bounces_list"($1,$2);'

/**
 * 会員一覧を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_id 会員ID
 * @param $3 in_companyName 会社名
 * @param $4 in_customerCode  取引先コード
 * @param $5 in_status ステータス
 */
const GET_MEMBERS_FUNCTION = 'SELECT * FROM "f_get_members"($1,$2,$3,$4,$5);'

/**
 * CSV用会員一覧を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_customer_node 取引先コード
 * @param $3 in_company_name 会社名
 * @param $4 in_status ステータス
 */
const GET_MEMBERS_CSV_FUNCTION =
  'SELECT * FROM "f_get_members_csv"($1,$2,$3,$4);'

/**
 * 会員情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_member_no 会員番号
 */
const GET_MEMBER_FUNCTION = 'SELECT * FROM "f_get_member"($1,$2);'

/**
 * 会員情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_member_no 会員番号
 */
const GET_MEMBER_FULL_FUNCTION = 'SELECT * FROM "f_get_member_full"($1,$2);'

/**
 * 会員ステータスを承認に更新する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_request_no 会員申請番号
 * @param $3 in_tanto_no 担当者
 * @param $4 in_status 元ステータス
 * @param $5 in_status ステータス
 * @param $6 in_update_admin_no 編集管理者番号
 */
const UPDATE_MEMBER_STATUS_APPROVE_FUNCTION =
  'SELECT * FROM "f_update_member_status_approve"($1,$2,$3,$4,$5,$6);'

/**
 * 承認以外の会員ステータスを更新する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_no 会員番号
 * @param $3 in_tanto_no 担当者
 * @param $4 in_status 元ステータス
 * @param $5 in_status ステータス
 * @param $6 in_update_admin_no 編集管理者番号
 */
const UPDATE_MEMBER_STATUS_OTHERS_FUNCTION =
  'SELECT * FROM "f_update_member_status_others"($1,$2,$3,$4,$5,$6);'

/**
 * 会員ステータス変更履歴を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_member_request_no 会員申請番号
 */
const GET_MEMBER_STATUS_HISTORY_FUNCTION =
  'SELECT * FROM "f_get_member_status_history"($1,$2);'

/**
 * 会員情報を更新する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_no 会員番号
 * @param $3 in_bid_allow_flag 入札可能フラグ
 * @param $4 in_email_delivery_flag メール配信フラグ
 * @param $5 in_email_priority メール配信重要度
 * @param $6 in_status ステータス
 * @param $7 in_password パスワード
 * @param $8 in_free_field 自由欄
 * @param $9 in_update_admin_no 編集管理者番号
 */
const UPDATE_MEMBER_FUNCTION =
  'SELECT * FROM "f_update_member"($1,$2,$3,$4,$5,$6,$7,$8,$9);'

/**
 * 会員申請情報を更新する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_request_no 会員申請番号
 * @param $3 in_email_priority メール重要度
 * @param $3 in_free_field 自由欄
 * @param $4 in_update_admin_no 編集管理者番号
 */
const UPDATE_MEMBER_REQUEST_FUNCTION =
  'SELECT * FROM "f_update_member_request"($1,$2,$3,$4,$5);'

/**
 * 定数キーを取得する
 */
const GET_CONSTANT_KEY_FUNCTION = 'SELECT * FROM "f_get_constant_key"();'

/**
 * ロット情報を取得する
 * @param $1 テナント番号
 * @param $2 入札会番号
 * @param $3 管理番号
 * @param $4 商品名
 * @param $5 カテゴリー名
 * @param $6 開始日時 From
 * @param $7 開始日時 To
 * @param $8 終了日時 From
 * @param $9 終了日時 To
 * @param $10 言語コード
 */
const GET_LOTS_FUNCTION =
  'SELECT * FROM "f_get_lots"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10);'

/**
 * ロット情報を登録・更新する
 * @param $1 開催回番号
 * @param $2 テナント番号
 * @param $3 商品番号配列
 * @param $4 ロットNo
 * @param $8 最低入札価格
 * @param $9 最低落札価格
 * @param $10 即決価格
 * @param $11 おすすめフラグ
 * @param $12 商品ごとの終了時間
 * @param $13 管理者番号
 * @returns return_lot_no ロット番号
 */
const UPSERT_LOT =
  'SELECT * FROM "f_upsert_lot"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13);'

/**
 * ロット内訳情報を削除する
 * @param $1 開催回番号
 * @param $2 テナント番号
 * @param $3 商品番号配列
 * @param $4 管理者番号
 * @returns return_lot_no ロット番号
 */
const DELETE_LOT_DETAIL = 'SELECT * FROM "f_delete_lot_detail"($1,$2,$3,$4);'

/**
 * 多言語のメール情報を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_exhibition_name character varying   開催回名
 */
const GET_EXHIBITION_EMAIL_LANGUAGE_LIST_FUNCTION =
  'SELECT * FROM "f_get_exhibition_email_language_list"($1,$2);'

/**
 * メール情報を登録する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_admin_no bigint 管理者番号
 * @param $3 in_chilEmails  JSON[] 多言語の入札会メール
 */
const REGIST_UPDATE_EXHIBITION_EMAIL_FUNCTION =
  'SELECT * FROM "f_regist_update_exhibition_email"($1,$2,$3);'

/**
 * 多言語のメール情報を登録する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_admin_no bigint 管理者番号
 * @param $3 in_emailLanguageReq  JSON[] 多言語の入札会メール
 */
const REGIST_UPDATE_EXHIBITION_EMAIL_LANGUAGE_FUNCTION =
  'SELECT * FROM "f_regist_update_exhibition_email_language"($1,$2,$3);'

/**
 * お問い合わせCSVを取得する
 * @param $1 テナント番号
 * @param $2 お問い合わせ開始日
 * @param $3 お問い合わせ終了日
 */
const GET_INQUIRIES_CSV_FUNCTION =
  'SELECT * FROM "f_get_inquiries_csv"($1, $2, $3);'

/**
 * 入札会状況情報を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_no_array 入札会番号
 * @param $4 in_manage_no 商品ID
 * @param $5 in_product_name 商品名
 * @param $6 in_status_array ステータス配列(0:未入札、1:入札あり、2:落札、3:流札)
 * @param $7 in_unread_array 未読配列(0：未読無、1:未読有)
 */
const GET_EXHIBITION_ITEM_STATUS_FUNCTION =
  'SELECT * FROM "f_get_exhibition_item_status"($1,$2,$3,$4,$5,$6,$7);'

/**
 * 入札履歴を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_no_array bigint[] 開催回番号配列
 */
const GET_BID_HISTORY_FUNCTION = 'SELECT * FROM "f_get_bid_history"($1,$2,$3);'

/**
 * 入札順位を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_no_array bigint[] 開催回番号配列
 */
const GET_BID_ORDER_FUNCTION = 'SELECT * FROM "f_get_bid_order"($1,$2,$3);'

/**
 * 入札上限額一覧を取得する
 * @param $1 in_member_no bigint 会員番号
 */
const GET_TENANT_CREDIT_REMAINING_FUNCTION =
  'SELECT * FROM "f_get_tenant_credit_remaining"($1);'

/**
 * 入札上限額を更新する
 * @param $1 in_tenant_no bigint                        テナント番号
 * @param $2 in_bid_limit_flag integer                  入札上限額設定フラグ
 * @param $3 in_bid_limit numeric                       入札上限額
 * @param $4 in_reset_type integer                      リセット形式
 * @param $5 in_reset_date integer                      リセット日付
 * @param $6 in_start_datetime timestamp with time zone 適用開始日
 * @param $7 in_end_datetime timestamp with time zone   適用終了日
 * @param $8 in_update_admin_no bigint                  編集管理者番号
 */
const UPDATE_TENANT_CREDIT_REMAINING_FUNCTION =
  'SELECT * FROM "f_upsert_tenant_credit_remaining"($1, $2, $3, $4, $5, $6, $7, $8);'

/**
 * 入札番号を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_name character varying 開催名
 */
const GET_EXHIBITION_BY_NAME_FUNCTION =
  'SELECT * FROM "f_get_exhibition_by_name"($1,$2,$3);'

/**
 * 入札会結果を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_exhibition_no_array bigint[] 開催回番号配列
 */
const GET_EXHIBITION_RESULT_FUNCTION =
  'SELECT * FROM "f_get_exhibition_result"($1,$2,$3);'

/**
 * 開催回集計を取得します
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_exhibition_no_array bigint[] 開催回番号配列
 */
const GET_EXHIBITION_SUMMARY_FUNCTION =
  'SELECT * FROM "f_get_exhibition_summary"($1,$2);'

/**
 * ダッシュボード画面用データを取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_name character varying 開催名
 * @param $4 in_category_id integer カテゴリ
 */
const GET_DASHBOARD_FUNCTION = 'SELECT * FROM "f_get_dashboard"($1,$2,$3,$4);'

/**
 * ダッシュボード画面用入札会名リストを取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_role_id bigint Role id
 */
const GET_EXHIBITION_PULLDOWN_FUNCTION =
  'SELECT * FROM "f_get_exhibition_pulldown"($1,$2);'

/**
 * お知らせメール画面用入札会名リストを取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying
 */
const GET_EXHIBITION_NAME_PULLDOWN_FUNCTION =
  'SELECT * FROM "f_get_exhibition_name_pulldown"($1,$2);'

/**
 * ダッシュボード画面用データを取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_exhibition_name character varying 開催名
 * @param $3 in_language_code character varying 言語コード(管理者はja固定)
 */
const GET_EXHIBITION_INFORMATION_FUNCTION =
  'SELECT * FROM "f_get_exhibition_information"($1,$2,$3);'

/**
 * 在庫機直接成約
 * @param $1 in_item_no 商品番号,
 * @param $2 in_member_no 成約会員番号,
 * @param $3 in_contract_price 成約金額,
 * @param $4 in_tenant_no テナントID,
 * @param $5 in_login_member_no ログイン管理番号
 */
const CONTRACT_STOCK_FUNCTION =
  'SELECT * FROM "f_contract_stock"($1,$2,$3,$4,$5);'

/**
 * 入札会情報を取得する
 * @param $1 テナント番号
 * @param $2 入札会番号
 */
const GET_EXHIBITION_LOTS_FUNCTION =
  'SELECT * FROM "f_get_exhibition_lots"($1,$2);'

/**
 * 入札会ステータスを取得する
 * @param $1 テナント番号
 * @param $2 入札会番号
 */
const GET_EXHIBITION_STATUS_FUNCTION =
  'SELECT * FROM "f_get_exhibition_status"($1,$2);'

/**
 * XLSX用LOT一覧を取得する
 * @param $1 入札会番号
 * @param $2 テナント番号
 * @param $3 Serial from
 * @param $4 Serial to
 * @param $5 言語コード
 */
const GET_LOTS_XLSX_FUNCTION =
  'SELECT * FROM "f_get_lots_xlsx"($1,$2,$3,$4,$5);'

/**
 * XLSX用LOTを入れる前にすべてLOTを削除する
 * @param $1 入札会番号
 * @param $2 テナント番号
 * @param $3 管理者番号
 * @param $4 言語コード
 */
const DELETE_ALL_LOTS_XLSX_FUNCTION =
  'SELECT * FROM "f_delete_all_lots_xlsx"($1,$2,$3,$4);'

/**
 * XLSX用Itemステータスを取得する
 * @param $1 テナント番号
 * @param $2 Item No
 */
const GET_ITEMS_STATUS_FUNCTION = 'SELECT * FROM "f_get_items_status"($1,$2);'

/**
 * お知らせメール一覧を取得する
 * @param $1 テナントID
 * @param $2 送信フラグ
 * @param $3 タイトル
 * @param $4 送信開始日時
 * @param $5 送信終了日時
 */
const GET_NOTICE_EMAIL_LIST_FUNCTION =
  'SELECT * FROM "f_get_notice_email_list"($1, $2, $3, $4, $5);'

/**
 * お知らせメール一覧を取得する
 * @param $1 テナント番号
 * @param $2 お知らせメール番号
 */
const GET_NOTICE_EMAIL_BY_NOTICE_EMAIL_NO_FUNCTION =
  'SELECT * FROM "f_get_notice_email_by_notice_email_no"($1, $2);'

/**
 * お知らせメールの添付ファイルを取得する
 * @param $1 テナント番号
 * @param $2 お知らせメール番号
 */
const GET_NOTICE_EMAIL_FILES_FUNCTION =
  'SELECT * FROM "f_get_notice_email_files"($1, $2);'

/**
 * お知らせメールを更新・登録する
 * @param $1 テナント番号
 * @param $2 お知らせ番号
 * @param $3 送信日付
 * @param $4 メール重要度
 * @param $5 言語コード
 * @param $6 タイトル
 * @param $7 本文タイトル(上段)
 * @param $8 本文タイトル(下段)
 * @param $9 本文
 * @param $10 フッター
 * @param $11 添付ファイルURL
 * @param $12 作成者番号
 * @param $13 更新者番号
 */
const INSERT_OR_UPDATE_NOTICE_EMAIL_FUNCTION =
  'SELECT * FROM "f_insert_or_update_notice_email"($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13);'

/**
 * お知らせメールを削除する
 * @param $1 テナント番号
 * @param $2 お知らせ番号
 * @param $3 更新者番号
 */
const DELETE_NOTICE_EMAIL_FUNCTION =
  'SELECT * FROM "f_delete_notice_email"($1, $2, $3);'

/**
 * お知らせメールを取得する
 * @param $1 テナント番号
 * @param $2 お知らせ番号
 */
const GET_NOTICE_EMAIL_FOR_SENDING_EMAIL_FUNCTION =
  'SELECT * FROM "f_get_notice_email_for_sending_email"($1, $2);'

/**
 * バリデーション用お知らせメールを取得する
 * @param $1 テナント番号
 * @param $2 お知らせ番号
 */
const GET_NOTICE_EMAIL_FOR_VALIDATION_FUNCTION =
  'SELECT * FROM "f_get_notice_email_for_validation"($1, $2);'

/**
 * 会員件数を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_member_id 会員番号
 * @param $3 in_companyName 会社名
 * @param $4 in_customerCode  取引先コード
 * @param $5 in_status ステータス
 */
const GET_MEMBERS_COUNT_FUNCTION =
  'SELECT COUNT(*) FROM "f_get_members"($1,$2,$3,$4,$5);'

/**
 * 商品件数を取得する
 * @param $1 ステータス
 * @param $2 新着マーク
 * @param $3 管理番号(from)
 * @param $4 管理番号(to)
 * @param $5 Serial(from)
 * @param $6 Serial(to)
 * @param $7 地区ID
 * @param $8 カテゴリー
 * @param $9 メーカー
 * @param $10 テナント番号
 * @param $11 言語コード
 */
const GET_ITEMS_COUNT =
  'SELECT COUNT(*) FROM "f_get_items"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11);'

/**
 * 入札会件数を取得する
 * @param $1 入札会No
 * @param $2 テナントNo
 * @param $3 開催地区ID
 * @param $4 下見開始日時(from)
 * @param $5 下見開始日時(to)
 * @param $6 開催回開始日時(from)
 * @param $7 開催回開始日時(to)
 */
const GET_EXHIBITIONS_COUNT =
  'SELECT COUNT(*) FROM "f_get_exhibitions"($1,$2,$3,$4,$5,$6,$7,$8);'

/**
 * お知らせ件数を取得する
 * @param $1 テナントID
 * @param $2 表示先コード
 * @param $3 表示開始日時
 * @param $4 表示終了日時
 */
const GET_NOTICE_LIST_COUNT =
  'SELECT COUNT(DISTINCT(notice_no)) FROM "f_get_notice_list"($1, $2, $3, $4);'

/**
 * お知らせメール件数を取得する
 * @param $1 テナントID
 * @param $2 送信フラグ
 * @param $3 タイトル
 * @param $4 送信開始日時
 * @param $5 送信終了日時
 */
const GET_NOTICE_EMAIL_LIST_COUNT =
  'SELECT COUNT(DISTINCT(notice_email_no)) FROM "f_get_notice_email_list"($1, $2, $3, $4, $5);'

/**
 * 不達メール件数を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_to_address character varying 宛先
 * @param $3 in_subject character varying タイトル
 * @param $4 in_start_datetime timestamp with time zone 適用開始日時
 * @param $5 in_end_datetime timestamp with time zone 適用終了日時
 */
const GET_EMAIL_NOTIFICATION_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_email_notification_list"($1,$2,$3,$4,$5);'

/**
 * 定数件数を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_key_string 定数キーワード
 * @param $3 in_get_mente_impossible_data_flag 編集不可フラグ（true:編集不可項目を取得、false:編集可能項目を取得）
 */
const GET_CONSTANT_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_constant_list"($1,$2,$3);'

/**
 * 多言語のメール件数を取得する
 * @param $1 in_tenant_no bigint テナントID
 * @param $2 in_language_code character varying 言語コード(管理者はja固定)
 * @param $3 in_exhibition_no_array 入札会番号
 * @param $4 in_manage_no 商品ID
 * @param $5 in_product_name 商品名
 * @param $8 in_status_array ステータス配列(0:未入札、1:入札あり、2:落札、3:流札)
 * @param $10 in_unread_array 未読配列(0：未読無、1:未読有)
 */
const GET_EXHIBITION_ITEM_STATUS_COUNT =
  'SELECT COUNT(*) FROM "f_get_exhibition_item_status"($1,$2,$3,$4,$5,$6,$7);'

/**
 * 項目設定一覧の件数を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_resource_string リソース文字列
 * @param $3 in_language_code_string 言語コード
 */
const GET_FIELD_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_field_list"($1,$2,$3);'

/**
 * ロット情報を登録・更新する
 * @param $1 Stockデータ
 * @param $2 テナント番号
 * @param $3 更新者番号
 */
const UPSERT_ITEM = 'SELECT * FROM "f_upsert_item"($1,$2,$3);'

/**
 * 在庫データのファイルを登録・更新する
 * @param $1 テナント番号
 * @param $2 item_no
 * @param $3 manage_no
 * @param $4 Imageデータ
 */
const INSERT_ITEM_FILES = 'SELECT * FROM "f_insert_item_files"($1,$2,$3,$4);'

/**
 * 出品情報を削除する
 * @param $1 テナント番号
 * @param $2 開催回番号
 * @param $3 ロットID
 * @param $4 管理者番号
 */
const DELETE_EXHIBITION_ITEM =
  'SELECT * FROM "f_delete_exhibition_item"($1,$2,$3,$4);'

/**
 * Manage_noで出品情報を取得する
 * @param $1 テナント番号
 * @param $2 Exhibition No
 * @param $3 Manage No
 * @param $4 language code
 */
const GET_ITEM_BY_MANAGE_NO =
  'SELECT * FROM "f_get_item_by_manage_no"($1,$2,$3,$4);'

/**
 * Ｅｘｃｅｌ用の在庫一覧を取得する
 * @param $1 テナント番号
 * @param $2 入札会番号
 * @param $3 言語コード
 */
const GET_ITEMS_XLSX_FUNCTION = 'SELECT * FROM "f_get_items_xlsx"($1,$2,$3);'

/**
 * Nicknameで会員情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_nickname ニックネーム
 */
const GET_MEMBER_BY_NICKNAME =
  'SELECT * FROM "f_get_member_by_nickname"($1,$2);'

/**
 * Emailで会員情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_email Email
 */
const GET_MEMBER_BY_EMAIL = 'SELECT * FROM "f_get_member_by_email"($1,$2);'

/**
 * ロットのCOUNTを取得する
 * @param $1 テナント番号
 * @param $2 入札会番号
 * @param $3 管理番号
 * @param $4 商品名
 * @param $5 カテゴリー名
 * @param $6 開始日時 From
 * @param $7 開始日時 To
 * @param $8 終了日時 From
 * @param $9 終了日時 To
 * @param $10 言語コード
 */

const GET_LOTS_COUNT_FUNCTION =
  'SELECT COUNT(*) FROM "f_get_lots"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) A;'

/**
 * バッチ処理結果件数を取得する
 * @param $1 実行日時(from)
 * @param $2 実行日時(to)
 * @param $3 エラー有無
 */
const GET_BATCH_RESULT_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_batch_result_list"($1,$2,$3);'

/**
 * バッチ処理結果を取得する
 * @param $1 実行日時(from)
 * @param $2 実行日時(to)
 * @param $3 エラー有無
 */
const GET_BATCH_RESULT_LIST =
  'SELECT * FROM "f_get_batch_result_list"($1,$2,$3);'

/**
 * バッチ処理結果詳細を取得する
 * @param $1 実行日時
 */
const GET_BATCH_RESULT_DETAIL = 'SELECT * FROM "f_get_batch_result_detail"($1);'

/**
 * 会員情報を更新する
 * @param $1 in_member_no 会員番号
 * @param $2 in_bid_allow_flag 入札可能フラグ
 * @param $3 in_email_delivery_flag メール配信フラグ
 * @param $4 in_memo 備考
 * @param $5 in_update_admin_no 編集管理者番号
 */
const UPDATE_MEMBER_EDIT_FUNCTION =
  'SELECT * FROM "f_update_member_edit"($1,$2,$3,$4,$5);'

/**
 * 問合せチャット情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 */
const GET_INQUIRY_CHAT_FUNCTION = 'SELECT * FROM "f_get_inquiry_chat"($1,$2);'

/**
 * 問合せチャット情報を削除する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_message_no 出品チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const DELETE_INQUIRY_CHAT_FUNCTION =
  'SELECT * FROM "f_delete_inquiry_chat"($1,$2,$3);'

/**
 * 問合せチャット情報の確認者を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_message_no 出品チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const UPDATE_INQUIRY_CHAT_FUNCTION =
  'SELECT * FROM "f_update_inquiry_chat_checked_admin"($1,$2,$3);'

/**
 * 問合せチャット情報を登録する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 * @param $3 in_exhibition_message_no 出品チャット番号
 * @param $4 in_chat_message チャットメッセージ
 * @param $5 in_admin_no 管理者番号
 */
const REGIST_INQUIRY_CHAT_FUNCTION =
  'SELECT * FROM "f_insert_inquiry_chat"($1,$2,$3,$4,$5);'

/**
 * お問い合わせチャット通知を登録する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_notification_category_id
 * @param $3 in_answer_exhibition_message_no
 * @param $4 in_exhibition_item_no
 * @param $5 in_user_no 会員番号
 * @param $6 in_admin_no 管理者番号
 */
const INSERT_INQUIRY_CHAT_NOTIFICATION =
  'SELECT * FROM "f_insert_inquiry_chat_notification"($1,$2,$3,$4,$5,$6);'

/**
 * お問い合わせチャットの表示・非表示を変更する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_message_no 出品チャット番号
 * @param $3 hidden_flag 非表示フラグ
 */
const EXCHANGE_DISPLAY_INQUIRY_CHAT_FUNCTION =
  'SELECT * FROM "f_exchange_display_inquiry_chat"($1,$2,$3);'

/**
 * 問い合わせチャットの未読カウント情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 */
const GET_INQUIRY_CHAT_UN_READ_CNT_FUNCTION =
  'SELECT * FROM "f_get_inquiry_chat_un_read_cnt"($1,$2);'

/**
 * 配送チャットの未読カウント情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 */
const GET_DELIVERY_CHAT_UN_READ_CNT_FUNCTION =
  'SELECT * FROM "f_get_delivery_chat_un_read_cnt"($1,$2);'

/**
 * 配送チャット情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 */
const GET_DELIVERY_CHAT_FUNCTION = 'SELECT * FROM "f_get_delivery_chat"($1,$2);'

/**
 * 配送チャット情報の確認者を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_delivery_message_no 配送チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const UPDATE_DELIVERY_CHAT_FUNCTION =
  'SELECT * FROM "f_update_delivery_chat_checked_admin"($1,$2,$3);'

/**
 * 配送チャット情報を登録する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_item_no 商品番号
 * @param $3 in_delivery_message_no 配送チャット番号
 * @param $4 in_chat_message チャットメッセージ
 * @param $5 in_admin_no 管理者番号
 */
const REGIST_DELIVERY_CHAT_FUNCTION =
  'SELECT * FROM "f_insert_delivery_chat"($1,$2,$3,$4,$5);'

/**
 * 商品チャットの無回答情報を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_exhibition_message_no 商品チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const UPDATE_INQUIRY_CHAT_NO_ANSWER_FUNCTION =
  'SELECT * FROM "f_update_inquiry_chat_no_answer"($1,$2,$3);'

/**
 * 配送チャットの無回答情報を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_delivery_message_no 配送チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const UPDATE_DELIVERY_CHAT_NO_ANSWER_FUNCTION =
  'SELECT * FROM "f_update_delivery_chat_no_answer"($1,$2,$3);'

/**
 * 配送チャット情報を削除する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_delivery_message_no 配送チャット番号
 * @param $3 in_admin_no 管理者番号
 */
const DELETE_DELIVERY_CHAT_FUNCTION =
  'SELECT * FROM "f_delete_delivery_chat"($1,$2,$3);'

/**
 * 会員登録
 * @param $1 tenant_no
 * @param $2 free_field JSONB フリーフィールド
 * @param $3 in_bid_allow_flag メール配信フラグ
 * @param $4 in_email_delivery_flag メール配信フラグ
 * @param $5 in_temporary_password 仮パスワード
 * @param $6 in_update_admin_no 編集管理者番号
 */
const CREATE_MEMBER_FUNCTION =
  'SELECT * FROM "f_create_member"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10);'

/**
 * 取引先コードで会員情報を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_customer_code Email
 */
const GET_MEMBER_BY_CUSTOMER_CODE =
  'SELECT * FROM "f_get_member_by_customer_code"($1,$2);'

/**
 * リソース選択と言語を使用して項目設定一覧を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_resource_string リソース文字列
 * @param $3 in_language_code_string 言語コード
 */
const GET_FIELD_LIST_FUNCTION = 'SELECT * FROM "f_get_field_list"($1,$2,$3);'

/**
 * テナントマスタから言語リストを取得する
 * @param $1 in_tenant_no テナントID
 */
const GET_TENANT_LANGUAGE_LIST_FUNCTION =
  'SELECT * FROM "f_get_tenant_language_list"($1);'

/**
 * 項目設定を登録・更新する
 * @param $1  in_field_no bigint 項目番号
 * @param $2  in_field_localized_no bigint 項目（多言語化）番号
 * @param $3  in_tenant_no bigint テナントID
 * @param $4  in_field_division character varying 項目区分
 * @param $5  in_physical_name character varying 物理名
 * @param $6  in_input_type character varying 入力タイプ
 * @param $7  in_input_data_list jsonb 入力リスト
 * @param $8  in_data_type character varying データの型
 * @param $9  in_max_length integer 最大文字数
 * @param $10 in_max_value integer varying 最大値
 * @param $11 in_regular_expressions character varying 正規表現
 * @param $12 in_required_flag integer 必須フラグ
 * @param $13 in_order_no integer 表示順
 * @param $14 in_language_code character varying 言語区分
 * @param $15 in_logical_name character varying 論理名
 */
const REGIST_UPDATE_FIELD_FUNCTION =
  'SELECT * FROM "f_regist_update_field"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15);'

/**
 * 項目設定マスタの特定の値を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_field_localized_no 項目（多言語化）番号
 */
const GET_FIELD_ITEM_FUNCTION = 'SELECT * FROM "f_get_field_item"($1,$2);'

/**
 * 項目設定マスタの特定の値を削除する
 * @param $1 in_field_no 項目番号
 * @param $2 in_field_localized_no 項目（多言語化）番号
 * @param $3 in_tenant_no テナントID
 */
const DELETE_FIELD_ITEM_FUNCTION =
  'SELECT * FROM "f_delete_field_item"($1,$2,$3);'

/**
 * テナント番号、項目区分、物理名、言語コードでの重複確認用（新規登録時）
 * @param $1 in_tenant_no テナントID
 * @param $2 in_field_division 項目区分
 * @param $3 in_physical_name 物理名
 * @param $4 in_language_code 言語コード
 */
const CHECK_FIELD_EXISTS = 'SELECT * FROM "f_check_field_item"($1,$2,$3,$4);'

/**
 * テナント番号、項目区分、物理名、項目番号での重複確認用（更新時）
 * @param $1 in_tenant_no テナントID
 * @param $2 in_field_division 項目区分
 * @param $3 in_physical_name 物理名
 * @param $4 in_field_no 項目番号
 */
const CHECK_FIELD_EXISTS_UPDATE =
  'SELECT * FROM "f_check_field_item_update"($1,$2,$3,$4);'

/**
 * 項目表示設定、CSV項目設定で更新対象のfield_noの使用状況確認
 * @param $1 in_field_no 項目番号
 * @param $2 in_tenant_no テナントID
 * @param $3 in_language_code 言語コード
 */
const CHECK_FIELD_ITEM_USED =
  'SELECT * FROM "f_check_field_item_used"($1,$2,$3);'

/**
 * 画面選択と言語を使用して項目表示設定一覧を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_window_id 画面ID
 * @param $3 in_language_code 言語コード
 */
const GET_DISPLAY_FIELD_LIST_FUNCTION =
  'SELECT * FROM "f_get_display_field_list"($1,$2,$3);'

/**
 * 項目表示設定一覧の件数を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_window_id 画面ID
 * @param $3 in_language_code 言語コード
 */
const GET_DISPLAY_FIELD_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_display_field_list"($1,$2,$3);'

/**
 * 言語を使用してすべての項目設定一覧を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_language_code 言語コード
 */
const GET_FIELD_ALL_LIST_FUNCTION =
  'SELECT * FROM "f_get_field_all_list"($1,$2);'

/**
 * 特定の言語のすべての項目設定一覧の件数を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_language_code 言語コード
 */
const GET_FIELD_ALL_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_field_all_list"($1,$2);'

/**
 * 項目表示設定一覧を登録・更新する
 * @param $1 in_field_mapping_no 項目マッピング番号
 * @param $2 in_tenant_no テナントID
 * @param $3 in_window_id 画面ID
 * @param $4 in_language_code 言語コード
 * @param $5 in_field_no 項目情報配列（JSONB形式）
 */
const REGIST_UPDATE_DISPLAY_FIELD_LIST_FUNCTION =
  'SELECT * FROM "f_regist_update_display_field_list"($1,$2,$3,$4,$5);'

/**
 * 項目表示設定一覧を削除する
 * @param $1 in_field_mapping_no 項目マッピング番号
 * @param $2 in_tenant_no テナントID
 */
const DELETE_DISPLAY_FIELD_LIST_FUNCTION =
  'SELECT * FROM "f_delete_display_field_list"($1,$2);'

/**
 * 取引先コードで会員情報を取得する
 * @param $1 in_origin
 */
const GET_TENANT_SETTING_FUNCTION = 'SELECT * FROM "f_get_tenant_setting"($1);'

/**
 * テナント設定を更新
 * @param $1 in_tenant_no bigint,
 * @param $2 in_tenant_name character varying,
 * @param $3 in_company_name character varying,
 * @param $4 in_contact_email character varying,
 * @param $5 in_two_factor_auth_required integer,
 * @param $6 in_currency_code character varying,
 * @param $7 in_language_code_list character varying[],
 * @param $8 in_search_result_view_mode character varying,
 */
const UPDATE_TENANT_SETTING_FUNCTION =
  'SELECT * FROM "f_update_tenant_setting"($1,$2,$3,$4,$5,$6,$7,$8);'

/**
 * 権限設定を取得
 * @param $1 in_tenant_no テナントID
 */
const GET_ADMIN_ROLE = 'SELECT * FROM "f_get_admin_role"($1);'

/**
 * 権限設定を更新
 * @param $1 in_tenant_no テナントID
 * @param $2 in_target_group_id 対象グループID
 * @param $3 in_function_id 機能ID
 * @param $4 in_allowed_role_id 許可される権限
 * @param $5 in_update_admin_no 更新管理者番号
 */
const UPDATE_ADMIN_ROLE = 'SELECT * FROM "f_update_admin_role"($1,$2,$3,$4,$5);'

/**
 * テナントオプションを取得する
 * @param $1 in_tenant_no テナント番号
 */
const GET_TENANT_OPTIONS = 'SELECT * FROM "f_get_tenant_options"($1);'

/**
 * テナントオプションを更新する
 * @param $1 in_tenant_no テナント番号
 * @param $2 in_function_options JSONB 関数オプション
 * @param $3 in_bid_options JSONB 入札オプション
 */
const UPDATE_TENANT_OPTIONS =
  'SELECT * FROM "f_update_tenant_options"($1, $2, $3);'

/**
 * Static page listを取得する
 * @param $1 テナントID
 * @param $2 page path
 */
const GET_STATIC_PAGE_LIST = 'SELECT * FROM "f_get_static_page_list"($1, $2);'

/**
 * Static pageを登録・更新する
 * @param $1 テナントID
 * @param $2 ページNo
 * @param $3 ページパス
 * @param $4 Localized JSONB[]
 * @param $5 作成者番号
 * @param $6 更新者番号
 */
const INSERT_OR_UPDATE_STATIC_PAGE =
  'SELECT * FROM "f_insert_or_update_static_page"($1, $2, $3, $4, $5, $6);'

/**
 * Static pageを取得する
 * @param $1 テナントID
 * @param $2 ページNo
 */
const GET_STATIC_PAGE_BY_NO = 'SELECT * FROM "f_get_static_page_by_no"($1, $2);'

/**
 * Static pageを削除する
 * @param $1 テナントID
 * @param $2 ページNo
 * @param $3 更新者番号
 */
const DELETE_STATIC_PAGE = 'SELECT * FROM "f_delete_static_page"($1, $2, $3);'

/**
 * テンプレートと言語を使用してCSV項目設定一覧を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_template_id テンプレート番号
 * @param $3 in_language_code 言語コード
 */
const GET_CSV_FIELD_LIST_FUNCTION =
  'SELECT * FROM "f_get_csv_field_list"($1,$2,$3);'

/**
 * CSV項目設定一覧の件数を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_template_id テンプレート番号
 * @param $3 in_language_code 言語コード
 */
const GET_CSV_FIELD_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_csv_field_list"($1,$2,$3);'

/**
 * CSV項目設定の編集不可入力リストを取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_template_id テンプレート番号
 * @param $3 in_language_code 言語コード
 */
const GET_NOT_EDITABLE_CSV_FIELD_LIST =
  'SELECT * FROM "f_get_not_editable_csv_field_list"($1,$2,$3);'

/**
 * CSV項目設定一覧を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_csv_template_id CSVテンプレート番号
 * @param $3 in_language_code 言語コード
 * @param $4 in_input_data_list 入力リスト
 */
const UPDATE_CSV_FIELD_LIST_FUNCTION =
  'SELECT * FROM "f_update_csv_field_list"($1,$2,$3,$4);'

/**
 * itemの項目設定を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_user_no ユーザ番号
 * @param $3 in_language_code 言語コード
 */
const GET_ITEM_FIELDS = `
  SELECT * FROM "f_get_item_fields"($1, $2);
`

/**
 * 外部連携設定を取得する
 * @param $1 in_tenant_no テナントID
 */
const GET_EXT_LINK_OPTIONS_FUNCTION =
  'SELECT * FROM "f_get_ext_link_options"($1);'

/**
 * 外部連携設定を更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_ext_link_no 外部連携番号
 * @param $3 in_ext_link_options 外部連携設定
 * @param $4 in_admin_no 管理者番号
 */
const UPSERT_EXT_LINK_OPTIONS_FUNCTION =
  'SELECT * FROM "f_upsert_ext_link_options"($1,$2,$3,$4);'

/**
 * 項目マッピング管理一覧の件数を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_external_system_no 外部システム番号
 * @param $3 in_target_object 言語コード
 */
const GET_ITEM_MAPPING_LIST_COUNT =
  'SELECT COUNT(*) FROM "f_get_item_mapping_list"($1,$2,$3);'

/**
 * 項目マッピング管理一覧を取得する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_external_system_no 外部システム番号
 * @param $3 in_target_object 言語コード
 */
const GET_ITEM_MAPPING_LIST_FUNCTION =
  'SELECT * FROM "f_get_item_mapping_list"($1,$2,$3);'

/**
 * 項目マッピング管理一覧を登録・更新する
 * @param $1 in_tenant_no テナントID
 * @param $2 in_external_system_no 外部システム番号
 * @param $3 in_target_object 対象
 * @param $4 in_external_field_mapping  JSON[] 項目マッピング管理一覧
 * @param $5 in_delete_external_field_mapping  JSON[] 削除対象項目マッピング管理一覧
 */
const REGIST_UPDATE_EXTERNAL_FIELD_MAPPING_LIST_FUNCTION =
  'SELECT * FROM "f_regist_update_external_field_mapping_list"($1,$2,$3,$4,$5);'

const FUNCTION_LIST = {
  GET_USER_FUNCTION,
  GET_ADMIN_FOR_LOGIN_FUNCTION,

  GET_NOTICE_LIST_FUNCTION,
  GET_NOTICE_BY_NOTICE_NO_FUNCTION,
  INSERT_OR_UPDATE_NOTICE_FUNCTION,
  DELETE_NOTICE_FUNCTION,
  GET_NOTICE_FILES_FUNCTION,

  GET_CONSTANT_LIST_FUNCTION,
  GET_CONSTANT_KEY_FUNCTION,
  GET_CONSTANTS_LANGUAGE_LIST_FUNCTION,
  REGIST_UPDATE_CONSTANTS_FUNCTION,
  DELETE_CONSTANT_LOCALIZED_FUNCTION,
  DELETE_CONSTANT_FUNCTION,
  GET_EMAIL_NOTIFICATION_LIST_FUNCTION,
  GET_USER_BOUNCES_LIST_FUNCTION,

  GET_EXHIBITIONS,
  UPSERT_EXHIBITIONS,
  DELETE_EXHIBITIONS,

  UPDATE_EXHIBITION_ITEM_DATETIME,
  CANCEL_EXHIBITION_ITEM,

  GET_ITEMS,
  GET_ITEM_DETAIL,
  UPDATE_ITEMS_RECOMMEND,

  GET_CONSTANTS_BY_KEYS,
  GET_IP_ADDRESS_FOR_ACCESS_CHECK,

  GET_ADMIN_LIST_FUNCTION,
  GET_ADMIN_BY_ADMIN_NO_FUNCTION,
  INSERT_OR_UPDATE_ADMIN_FUNCTION,
  GET_ADMIN_BY_LOGIN_ID_FUNCTION,

  GET_MEMBERS_FUNCTION,
  GET_MEMBER_FUNCTION,
  GET_MEMBER_FULL_FUNCTION,
  UPDATE_MEMBER_STATUS_APPROVE_FUNCTION,
  UPDATE_MEMBER_STATUS_OTHERS_FUNCTION,
  GET_MEMBER_STATUS_HISTORY_FUNCTION,
  UPDATE_MEMBER_FUNCTION,
  UPDATE_MEMBER_REQUEST_FUNCTION,
  GET_MEMBERS_CSV_FUNCTION,
  CREATE_MEMBER_FUNCTION,
  GET_MEMBER_BY_CUSTOMER_CODE,

  GET_LOTS_FUNCTION,
  UPSERT_LOT,
  DELETE_LOT_DETAIL,
  GET_EXHIBITION_EMAIL_LANGUAGE_LIST_FUNCTION,
  REGIST_UPDATE_EXHIBITION_EMAIL_FUNCTION,
  REGIST_UPDATE_EXHIBITION_EMAIL_LANGUAGE_FUNCTION,
  GET_EXHIBITION_ITEM_STATUS_FUNCTION,
  GET_BID_HISTORY_FUNCTION,
  GET_BID_ORDER_FUNCTION,
  GET_TENANT_CREDIT_REMAINING_FUNCTION,
  UPDATE_TENANT_CREDIT_REMAINING_FUNCTION,
  GET_EXHIBITION_BY_NAME_FUNCTION,
  GET_EXHIBITION_RESULT_FUNCTION,
  GET_EXHIBITION_SUMMARY_FUNCTION,
  UPDATE_LOT_ID_PREFIX_FUNCTION,
  GET_EXHIBITION_LOTS_FUNCTION,
  GET_EXHIBITION_STATUS_FUNCTION,
  GET_LOTS_XLSX_FUNCTION,
  DELETE_ALL_LOTS_XLSX_FUNCTION,
  GET_ITEMS_STATUS_FUNCTION,
  GET_INQUIRIES_CSV_FUNCTION,
  GET_DASHBOARD_FUNCTION,
  GET_EXHIBITION_PULLDOWN_FUNCTION,
  GET_EXHIBITION_NAME_PULLDOWN_FUNCTION,
  GET_EXHIBITION_INFORMATION_FUNCTION,
  CONTRACT_STOCK_FUNCTION,

  GET_NOTICE_EMAIL_LIST_FUNCTION,
  GET_NOTICE_EMAIL_BY_NOTICE_EMAIL_NO_FUNCTION,
  GET_NOTICE_EMAIL_FILES_FUNCTION,
  INSERT_OR_UPDATE_NOTICE_EMAIL_FUNCTION,
  DELETE_NOTICE_EMAIL_FUNCTION,
  GET_NOTICE_EMAIL_FOR_SENDING_EMAIL_FUNCTION,
  GET_NOTICE_EMAIL_FOR_VALIDATION_FUNCTION,

  GET_MEMBERS_COUNT_FUNCTION,
  GET_ITEMS_COUNT,
  GET_EXHIBITIONS_COUNT,
  GET_NOTICE_LIST_COUNT,
  GET_NOTICE_EMAIL_LIST_COUNT,
  GET_EMAIL_NOTIFICATION_LIST_COUNT,
  GET_CONSTANT_LIST_COUNT,
  GET_EXHIBITION_ITEM_STATUS_COUNT,
  GET_FIELD_LIST_COUNT,

  UPSERT_ITEM,
  INSERT_ITEM_FILES,
  DELETE_EXHIBITION_ITEM,
  GET_ITEM_BY_MANAGE_NO,
  GET_ITEMS_XLSX_FUNCTION,
  GET_MEMBER_BY_NICKNAME,
  GET_MEMBER_BY_EMAIL,
  GET_LOTS_COUNT_FUNCTION,
  GET_BATCH_RESULT_LIST_COUNT,
  GET_BATCH_RESULT_LIST,
  GET_BATCH_RESULT_DETAIL,
  REPUBLISH_EXHIBITION_ITEM,

  UPDATE_MEMBER_EDIT_FUNCTION,

  GET_INQUIRY_CHAT_FUNCTION,
  DELETE_INQUIRY_CHAT_FUNCTION,
  UPDATE_INQUIRY_CHAT_FUNCTION,
  REGIST_INQUIRY_CHAT_FUNCTION,
  INSERT_INQUIRY_CHAT_NOTIFICATION,
  EXCHANGE_DISPLAY_INQUIRY_CHAT_FUNCTION,

  GET_INQUIRY_CHAT_UN_READ_CNT_FUNCTION,
  GET_DELIVERY_CHAT_UN_READ_CNT_FUNCTION,
  GET_DELIVERY_CHAT_FUNCTION,
  UPDATE_DELIVERY_CHAT_FUNCTION,
  REGIST_DELIVERY_CHAT_FUNCTION,
  UPDATE_INQUIRY_CHAT_NO_ANSWER_FUNCTION,
  UPDATE_DELIVERY_CHAT_NO_ANSWER_FUNCTION,
  DELETE_DELIVERY_CHAT_FUNCTION,

  GET_TENANT_LANGUAGE_LIST_FUNCTION,
  GET_FIELD_LIST_FUNCTION,
  REGIST_UPDATE_FIELD_FUNCTION,
  GET_FIELD_ITEM_FUNCTION,
  DELETE_FIELD_ITEM_FUNCTION,
  CHECK_FIELD_EXISTS,
  CHECK_FIELD_EXISTS_UPDATE,
  CHECK_FIELD_ITEM_USED,

  GET_DISPLAY_FIELD_LIST_FUNCTION,
  GET_DISPLAY_FIELD_LIST_COUNT,
  GET_FIELD_ALL_LIST_FUNCTION,
  GET_FIELD_ALL_LIST_COUNT,
  REGIST_UPDATE_DISPLAY_FIELD_LIST_FUNCTION,
  DELETE_DISPLAY_FIELD_LIST_FUNCTION,

  GET_TENANT_OPTIONS,
  UPDATE_TENANT_OPTIONS,
  GET_STATIC_PAGE_LIST,
  INSERT_OR_UPDATE_STATIC_PAGE,
  GET_STATIC_PAGE_BY_NO,
  DELETE_STATIC_PAGE,

  GET_TENANT_SETTING_FUNCTION,
  UPDATE_TENANT_SETTING_FUNCTION,
  GET_ADMIN_ROLE,
  UPDATE_ADMIN_ROLE,

  GET_CSV_FIELD_LIST_FUNCTION,
  GET_CSV_FIELD_LIST_COUNT,
  GET_NOT_EDITABLE_CSV_FIELD_LIST,
  UPDATE_CSV_FIELD_LIST_FUNCTION,

  GET_ITEM_FIELDS,

  GET_EXT_LINK_OPTIONS_FUNCTION,
  UPSERT_EXT_LINK_OPTIONS_FUNCTION,

  GET_ITEM_MAPPING_LIST_COUNT,
  GET_ITEM_MAPPING_LIST_FUNCTION,
  REGIST_UPDATE_EXTERNAL_FIELD_MAPPING_LIST_FUNCTION,

  SEARCH_MEMBER_BY_NAME,
  REGIST_EXHIBITION_MEMBERS,
}

module.exports = FUNCTION_LIST
