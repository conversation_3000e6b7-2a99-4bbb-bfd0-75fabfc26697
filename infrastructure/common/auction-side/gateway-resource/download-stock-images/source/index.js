const {GetObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner');
const {Upload} = require('@aws-sdk/lib-storage');
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Archiver = require('archiver');
const Stream = require('node:stream');

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  console.log(params);
  const base = new Base(pool, params.languageCode);
  const client = new S3Client({});
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant =>
      pool.query('SELECT * FROM "f_get_stock_images"($1,$2,$3);', [
        params.exhibitionItemNo,
        tenant.tenant_no,
        base.language,
      ])
    )
    .then(result => {
      return new Promise((resolve, reject) => {
        const archive = Archiver('zip');

        archive.on('error', reject);
        const stream = new Stream.PassThrough();
        archive.pipe(stream);
        stream.on('error', reject);
        Promise.all(
          result.map(file => {
            const command = new GetObjectCommand({
              Bucket: process.env.S3_BUCKET,
              Key: file.file_path,
            });
            const {Body} = client.send(command);
            console.log(command);
            return archive.append(Body, {
              name: file.file_path.replace(/^.*[\\\/]/, ''),
            });
          })
        ).then(() => {
          archive.finalize();
        });

        // Upload stream to S3
        const key = [
          'csv-download/',
          new Date().toString(),
          '-',
          base.randomString(10),
          '/',
          'ancillary',
          '-',
          new Date().toString(),
          '.zip',
        ].join('');
        console.log('key', key);
        const upload = new Upload({
          client,
          params: {
            Bucket: process.env.S3_BUCKET,
            Key: key,
            Body: stream,
          },
        });
        return upload.done().then(() => Promise.resolve(key));
      });
    })
    .then(key => {
      const expiresIn = Number.parseInt(process.env.SIGNED_URL_EXPIRES, 10);
      const command = new GetObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: key,
      });
      return getSignedUrl(client, command, {expiresIn});
    })
    .then(result => {
      const data = {
        url: result,
      };
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
