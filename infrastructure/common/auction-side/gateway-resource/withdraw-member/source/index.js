const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      return pool
        .query('SELECT * FROM "f_update_member_info"($1,$2,$3,$4,$5);', [
          tenant.tenant_no,
          authorizer.member_no,
          {},
          null,
          9,
        ])
        .then(members => {
          if (members.length === 0) {
            return Promise.reject({
              message: '会員情報が見つかりません',
            });
          }
          const member = members[0];

          return Promise.all([
            Promise.resolve()
              .then(() => {
                const language = member.free_field?.emailLang || 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    [
                      'EMAIL_MEMBER_WITHDRAWAL_FOR_MEMBER',
                      'EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN',
                      'EMAIL_COMMON_FOOTER',
                      'EMAIL_FROM',
                    ],
                    language,
                  ]
                );
              })
              .then(constants => {
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const footer =
                  constants.find(
                    constant => constant.key_string === 'EMAIL_COMMON_FOOTER'
                  ) || {};
                const mail =
                  constants.find(
                    constant =>
                      constant.key_string ===
                      'EMAIL_MEMBER_WITHDRAWAL_FOR_MEMBER'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = [member.free_field.email];
                const bcc = mail.value3 ? mail.value3.split(',') : [];
                const content = Common.format(mail.value4, [footer.value4]);

                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
            Promise.resolve()
              .then(() => {
                const language = 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    ['EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN', 'EMAIL_FROM'],
                    language,
                  ]
                );
              })
              .then(constants => {
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = mail.value3 ? mail.value3.split(',') : [];
                const bcc = [];

                // Tel
                const tel = member.free_field.telCountryCode
                  ? `${member.free_field.telCountryCode} ${member.free_field.tel}`
                  : member.free_field.tel;

                const content = Common.format(mail.value4, [
                  member.free_field.companyName || '',
                  member.member_id || '',
                  member.free_field.companyName || '',
                  tel || '',
                  member.free_field.email || '',
                ]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
          ]);
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
