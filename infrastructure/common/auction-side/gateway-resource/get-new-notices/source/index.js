const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      return Promise.all([
        pool.query('SELECT COUNT(*) FROM "f_get_new_notices"($1,$2,$3,$4);', [
          tenant.tenant_no,
          base.language,
          null,
          params.display_code !== null &&
          typeof params.display_code !== 'undefined'
            ? [].concat(params.display_code)
            : null,
        ]),
        pool.query('SELECT * FROM "f_get_new_notices"($1,$2,$3,$4);', [
          tenant.tenant_no,
          base.language,
          params.limit,
          params.display_code !== null &&
          typeof params.display_code !== 'undefined'
            ? [].concat(params.display_code)
            : null,
        ]),
      ]).then(datas => {
        console.log('datas', datas);
        const [countData, data] = datas;
        const res = {
          total_count:
            countData && countData.length > 0 ? countData[0].count : 0,
          notices: data || [],
        };
        return Promise.resolve(res);
      });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
