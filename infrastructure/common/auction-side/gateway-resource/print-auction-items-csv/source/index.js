const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      const sql_params = [e.authorizer.tenant_no, ['AREA_CODE'], base.language];
      console.log(`sql_params = ${JSON.stringify(sql_params)}`);
      return pool
        .query(
          'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
          sql_params
        )
        .then(constants => {
          return Promise.resolve({
            tenant,
            constants,
          });
        });
    })
    .then(data => {
      const tenant = data.tenant;
      const constants = data.constants;

      return pool
        .query(
          'SELECT * FROM "f_search_auction_items_for_csv"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16);',
          [
            tenant.tenant_no,
            params.auction_classification,
            params.searchKey,
            params.areas,
            params.category && params.category.length > 0
              ? params.category
              : null,
            params.startYear,
            params.endYear,
            params.startPrice,
            params.endPrice,
            params.favorite,
            params.bidding,
            params.unSoldOut,
            params.exceedingLowestPrice,
            params.currentPriceSort,
            base.language,
            authorizer.member_no,
          ]
        )
        .then(items => {
          if (items) {
            items.map(x => {
              // 2022.02.21: 在庫地の代わりに開催地区をだす
              if (x.areaId) {
                const area = constants.filter(
                  y => y.key_string === 'AREA_CODE' && y.value1 === x.areaId
                );
                x.storageLocation =
                  area && area.length > 0 ? area[0].value2 : '';
              } else {
                x.storageLocation = '';
              }
            });
          }
          return Promise.resolve(items);
        })
        .then(data => {
          return base.uploadCsvToS3(
            data,
            base.define.csvDefine.ITEM_LIST_CSV,
            'auction-items'
          );
        });
    })
    .then(result => {
      const data = {
        url: result,
      };
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
