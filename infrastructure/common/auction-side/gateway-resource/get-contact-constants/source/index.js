const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      return pool
        .query('SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);', [
          tenant.tenant_no,
          ['COUNTRY_CODE'],
          base.language,
        ])
        .then(result => {
          if (authorizer.member_no) {
            return pool
              .query('SELECT * FROM "f_get_member_info_by_member_no"($1,$2);', [
                authorizer.member_no,
                tenant.tenant_no,
              ])
              .then(members => {
                const member = members && members.length > 0 ? members[0] : {};
                return Promise.resolve({
                  constants: result,
                  member: {
                    address: [
                      member.free_field.state ? member.free_field.state : '',
                      member.free_field.city ? member.free_field.city : '',
                      member.free_field.address
                        ? member.free_field.address
                        : '',
                    ].join(''),
                    postCode: member.free_field.postCode,
                    companyName: member.free_field.companyName,
                    countryCode: member.free_field.countryCode,
                    email: member.free_field.email,
                    name: member.free_field.emailUserName,
                    tel: member.free_field.tel,
                  },
                  memberId: member.member_id,
                });
              });
          } else {
            return Promise.resolve({
              constants: result,
            });
          }
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
