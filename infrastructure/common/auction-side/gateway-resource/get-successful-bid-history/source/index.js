const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);

  console.log(`params = ${JSON.stringify(params)}`);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      // Validation
      const errors = {};
      const start_datetime = params.start_datetime;
      const end_datetime = params.end_datetime;

      // 必須チェック
      if (
        typeof start_datetime === 'undefined' ||
        start_datetime === null ||
        start_datetime === ''
      ) {
        console.log(`start_datetime = ${start_datetime}`);
        errors.start_datetime = base.define.message.E000017;
        const error = {
          status: 400,
          errors,
        };
        return Promise.reject(error);
      }

      if (
        typeof end_datetime === 'undefined' ||
        end_datetime === null ||
        end_datetime === ''
      ) {
        console.log(`end_datetime = ${end_datetime}`);
        errors.end_datetime = base.define.message.E000017;
        const error = {
          status: 400,
          errors,
        };
        return Promise.reject(error);
      }

      // 日付整合性チェック
      if (start_datetime !== '') {
        const tmpDate = new Date(start_datetime);
        console.log(`start_datetime = ${tmpDate}`);
        if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
          errors.start_datetime = base.define.message.E000018;
          const error = {
            status: 400,
            errors,
          };
          return Promise.reject(error);
        }
      }

      if (end_datetime !== '') {
        const tmpDate = new Date(end_datetime);
        console.log(`end_datetime = ${tmpDate}`);
        if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
          errors.end_datetime = base.define.message.E000018;
          const error = {
            status: 400,
            errors,
          };
          return Promise.reject(error);
        }
      }

      // 日付の前後関係が不正な場合
      if (start_datetime !== '' && end_datetime !== '') {
        const tmpStDate = new Date(start_datetime);
        const tmpEnDate = new Date(end_datetime);
        if (tmpStDate.getTime() > tmpEnDate.getTime()) {
          errors.start_datetime = base.define.message.E000019;
          errors.end_datetime = base.define.message.E000019;
          const error = {
            status: 400,
            errors,
          };
          return Promise.reject(error);
        }

        // 日付From-Toが3か月以内のチェック
        // const compareDate = new Date(end_datetime)
        // compareDate.setMonth(compareDate.getMonth() - 3)
        // console.log('compareDate = ' + compareDate)
        // if (compareDate.getTime() >= tmpStDate.getTime()) {
        //   errors['start_datetime'] = base.define.message.E000020
        //   errors['end_datetime'] = base.define.message.E000020
        //   const error = {
        //     status  : 400,
        //     errors
        //   }
        //   return Promise.reject(error)
        // }
      }
      // Validation successful
      return Promise.resolve();
    })
    .then(() => {
      const auctionClassification = params.auction_classification;
      if (
        typeof auctionClassification === 'undefined' ||
        auctionClassification === null ||
        auctionClassification.length <= 0
      ) {
        // Get constant
        const sqlParams = [
          e.authorizer.tenant_no,
          ['AUCTION_CLASSIFICATION'],
          base.language,
        ];

        return new Promise((resolve, reject) => {
          pool
            .query(
              'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
              sqlParams
            )
            .then(constants => {
              const constantsResult = [];
              for (let i = 0; i < constants.length; i++) {
                constantsResult.push(constants[i].value1);
              }
              console.log(`constantsResult = ${constantsResult}`);
              return resolve(constantsResult);
            })
            .catch(error => {
              return reject(error);
            });
        });
      } else {
        return Promise.resolve(auctionClassification);
      }
    })
    .then(constants => {
      let auctionClassification = params.auction_classification;
      if (
        typeof auctionClassification !== 'undefined' &&
        auctionClassification.length > 0
      ) {
        auctionClassification = auctionClassification.map(i => Number(i));
      } else {
        auctionClassification = constants.map(i => Number(i));
      }

      const options = {year: 'numeric', month: '2-digit', day: '2-digit'};

      let startDateTime = params.start_datetime;
      if (startDateTime === '') {
        startDateTime = null;
      } else {
        const tmpDate = new Date(startDateTime);
        startDateTime = `${tmpDate.toLocaleDateString('ja-JP', options)} 00:00:00`;
      }
      let endDateTime = params.end_datetime;
      if (endDateTime === '') {
        endDateTime = null;
      } else {
        const tmpDate = new Date(endDateTime);
        endDateTime = `${tmpDate.toLocaleDateString('ja-JP', options)} 23:59:59`;
      }

      const sql_params = [
        e.authorizer.tenant_no,
        e.authorizer.member_no,
        auctionClassification,
        params.searchKey ? [].concat(params.searchKey) : null,
        params.categoryList,
        startDateTime,
        endDateTime,
        base.language,
        params.limit,
      ];

      return pool.query(
        'SELECT * FROM "f_get_successful_bid_history"($1,$2,$3,$4,$5,$6,$7,$8,$9);',
        sql_params
      );
    })
    .then(result => {
      const data = result.length > 0 && result[0].data ? result[0].data : [];
      const items = data.items ? data.items : [];
      const isMoreLimit = params.limit && items.length > params.limit;
      const response = {
        items,
        count: data.count,
        exhibition_group: data.exhibition_group ? data.exhibition_group : [],
        isMoreLimit,
      };
      return base.createSuccessResponse(cb, response);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
