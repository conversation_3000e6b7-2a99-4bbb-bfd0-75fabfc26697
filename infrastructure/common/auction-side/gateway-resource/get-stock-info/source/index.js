const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => validator.validate(params))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant =>
      pool.query('SELECT * FROM "f_get_stock_info"($1,$2,$3,$4);', [
        params.exhibitionItemNo,
        tenant.tenant_no,
        base.language,
        authorizer.member_no,
      ])
    )
    .then(result => {
      const data = result.length > 0 ? result[0] : null;
      if (result.length > 0) {
        return base.createSuccessResponse(cb, data);
      } else {
        const response = {
          status: 400,
          message: base.define.message.E000004,
        };
        return reject(response);
      }
    })
    .catch(error => base.createErrorResponse(cb, error));
};
