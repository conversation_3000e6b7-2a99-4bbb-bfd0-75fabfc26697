const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const base = new Base(pool, params.languageCode);

  const sql_params = [
    e.authorizer.tenant_no,
    ['AUCTION_CLASSIFICATION', 'AREA_CODE'],
    base.language,
  ];
  console.log('sql_params = ' + JSON.stringify(sql_params));

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() =>
      pool.query(
        'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
        sql_params
      )
    )
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
