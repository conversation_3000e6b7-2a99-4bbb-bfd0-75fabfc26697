const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);
  let tenant = null;

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base
        .checkOrigin(header.origin || header.Origin || e.context?.access_ip)
        .then(data => {
          tenant = data;
        });
    })
    .then(() => {
      const country = params.registerData?.country;
      console.log(`country = ${country}`);
      if (country === 'JP') {
        const rules = require('./jp-validation-rules');
        return Promise.resolve(rules);
      }
      const rules = require('./other-validation-rules');
      return Promise.resolve(rules);
    })
    .then(rules => {
      console.log('INPUT VALIDATION');
      const registerData = params.registerData;
      const checkData = Object.assign({}, registerData);

      // Check if the password is being changed
      if (checkData.password || checkData.currentPassword) {
        rules.currentPassword.REQUIRED_CHECK = true;
        rules.password.REQUIRED_CHECK = true;
      } else {
        rules.currentPassword.REQUIRED_CHECK = false;
        rules.password.REQUIRED_CHECK = false;
      }

      return (
        Promise.resolve()
          // .then(() => {
          //   console.log('Check Duplicated Email')
          //   console.log('Email: ', checkData.email)
          //   checkData.emailDuplicated = 0
          //   if (checkData?.email) {
          //     return pool.query('SELECT * FROM "f_get_member_by_email"($1,$2);', [
          //       tenant.tenant_no,
          //       checkData.email
          //     ])
          //       .then(members => {
          //         console.log('members: ', members)
          //         if (members && members.length > 0) {
          //           checkData.emailDuplicated = -1
          //         }
          //         console.log('checkData.emailDuplicated: ', checkData.emailDuplicated)
          //         return Promise.resolve()
          //       })
          //   }
          //   return Promise.resolve()
          // })
          .then(() => {
            console.log('FINAL VALIDATION');
            return validator.validate(checkData, rules);
          })
      );
    })
    .then(() => {
      if (params.registerData.password && params.registerData.currentPassword) {
        return base
          .passwordVerify(
            params.registerData.currentPassword,
            authorizer.user_id,
            tenant.tenant_no
          )
          .then(() => {
            return Promise.resolve();
          })
          .catch(error => {
            if (error.status === 401) {
              return Promise.reject({
                status: 400,
                errors: {
                  currentPassword: base.define.message.E000021,
                },
              });
            }
            return Promise.reject(error);
          });
      }
      return Promise.resolve();
    })
    .then(() => {
      if (params.validateFlag) {
        return Promise.resolve({});
      }
      return Promise.resolve()
        .then(() => {
          if (params.registerData.password) {
            return base.hashPassword(params.registerData.password);
          }
          return Promise.resolve(null);
        })
        .then(newPassword => {
          const {password, currentPassword, ...freeField} = params.registerData;
          return pool.query(
            'SELECT * FROM "f_update_member_info"($1,$2,$3,$4);',
            [
              tenant.tenant_no,
              authorizer.member_no,
              {
                ...freeField,
              },
              newPassword,
            ]
          );
        })
        .then(result => {
          console.log('result: ', result);
          const member = result?.length > 0 ? result[0] : null;
          if (!member) {
            return Promise.reject({
              status: 400,
              errors: {
                common: base.define.message.E900001,
              },
            });
          }

          return Promise.all([
            Promise.resolve()
              .then(() => {
                const language = member.free_field?.emailLang || 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    [
                      'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER',
                      'EMAIL_COMMON_FOOTER',
                      'EMAIL_FROM',
                    ],
                    language,
                  ]
                );
              })
              .then(constants => {
                const footer =
                  constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
                  {};
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = [params.registerData.email];
                const bcc = mail.value3 ? mail.value3.split(',') : [];
                const content = Common.format(mail.value4, [
                  footer.value4 || '',
                ]);

                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
            Promise.resolve()
              .then(() => {
                const language = 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    ['EMAIL_CHANGE_MEMBER_INFO_FOR_ADMIN', 'EMAIL_FROM'],
                    language,
                  ]
                );
              })
              .then(constants => {
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_CHANGE_MEMBER_INFO_FOR_ADMIN'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = mail.value3 ? mail.value3.split(',') : [];
                const bcc = [];

                // Tel
                const tel = member.free_field.telCountryCode
                  ? `${member.free_field.telCountryCode} ${member.free_field.tel}`
                  : member.free_field.tel;

                const content = Common.format(mail.value4, [
                  member.free_field?.companyName || '',
                  member.member_id || '',
                  member.free_field?.companyName || '',
                  tel || '',
                  member.free_field?.email || '',
                ]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
          ]);
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
