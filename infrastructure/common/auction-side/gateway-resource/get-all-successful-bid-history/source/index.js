const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  console.log('🚀 log of params:', params);
  const header = e.headers;
  const base = new Base(pool, params.languageCode);
  let tenant = null;

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base.checkOrigin(header.origin || header.Origin).then(data => {
        tenant = data;
        return Promise.resolve();
      });
    })
    .then(() => {
      const auctionClassification = params.auction_classification;
      if (
        typeof auctionClassification === 'undefined' ||
        auctionClassification === null ||
        auctionClassification.length <= 0
      ) {
        // Get constant
        const sqlParams = [
          tenant.tenant_no,
          ['AUCTION_CLASSIFICATION'],
          base.language,
        ];

        return new Promise((resolve, reject) => {
          pool
            .query(
              'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
              sqlParams
            )
            .then(constants => {
              const constantsResult = [];
              for (let i = 0; i < constants.length; i++) {
                constantsResult.push(constants[i].value1);
              }
              console.log(`constantsResult = ${constantsResult}`);
              return resolve(constantsResult);
            })
            .catch(error => {
              return reject(error);
            });
        });
      } else {
        return Promise.resolve(auctionClassification);
      }
    })
    .then(constants => {
      let auctionClassification = params.auction_classification;
      if (
        typeof auctionClassification !== 'undefined' &&
        auctionClassification.length > 0
      ) {
        auctionClassification = auctionClassification.map(i => Number(i));
      } else {
        auctionClassification = constants.map(i => Number(i));
      }

      const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
      // get 6 months ago
      const tmpDate = new Date();
      tmpDate.setMonth(tmpDate.getMonth() - 6);
      const startDateTime = `${tmpDate.toLocaleDateString('ja-JP', options)} 00:00:00`;
      const endDateTime = `${new Date().toLocaleDateString('ja-JP', options)} 23:59:59`;

      const sql_params = [
        tenant.tenant_no,
        null, // member_no
        auctionClassification,
        params.searchKey ? [].concat(params.searchKey) : null,
        params.categoryList,
        startDateTime,
        endDateTime,
        base.language,
        params.limit,
      ];

      return pool.query(
        'SELECT * FROM "f_get_all_successful_bid_history"($1,$2,$3,$4,$5,$6,$7,$8,$9);',
        sql_params
      );
    })
    .then(result => {
      const data = result.length > 0 && result[0].data ? result[0].data : [];
      const items = data.items ? data.items : [];
      const isMoreLimit = params.limit && items.length > params.limit;
      const response = {
        items,
        count: data.count,
        exhibition_group: data.exhibition_group ? data.exhibition_group : [],
        isMoreLimit,
      };
      return base.createSuccessResponse(cb, response);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
