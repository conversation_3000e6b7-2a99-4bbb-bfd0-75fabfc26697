const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      return pool
        .query(
          'SELECT * FROM "f_search_stock_items_for_csv"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14);',
          [
            tenant.tenant_no,
            params.searchKey,
            params.areas && params.areas.length > 0 ? params.areas : null,
            params.category && params.category.length > 0
              ? params.category
              : null,
            params.startYear,
            params.endYear,
            params.startPrice,
            params.endPrice,
            params.favorite,
            params.bidding,
            params.unSoldOut,
            params.exceedingLowestPrice,
            base.language,
            authorizer.member_no,
          ]
        )
        .then(data => {
          return base.uploadCsvToS3(
            data,
            base.define.csvDefine.ITEM_LIST_CSV,
            'stock-items'
          );
        });
    })
    .then(result => {
      const data = {
        url: result,
      };
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
