const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool();
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(async () => {
      const sqlParams = [
        authorizer.tenant_no,
        authorizer.member_no,
        authorizer.user_no,
        params.lang,
      ];
      await pool.query(
        'SELECT * FROM "f_toggle_languages"($1, $2, $3, $4);',
        sqlParams
      );
      return params.lang;
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    });
};
