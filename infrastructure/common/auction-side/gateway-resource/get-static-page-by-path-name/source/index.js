const Define = require(process.env.COMMON_LAYER_PATH + 'define')
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js')
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js')
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js')
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body)
  console.log('🔑params = ' + JSON.stringify(params))
  const tenantNo = Common.extractTenantId(e);

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sql = 'select * from f_get_static_page_by_path($1,$2)'
      const sql_params = [tenantNo, params['page_path']]

      console.log('sql = ' + JSON.stringify(sql))
      console.log('sql_params = ' + JSON.stringify(sql_params))

      return pool.rlsQuery(tenantNo, sql, sql_params)
    })
    .then(page => {
      console.log('✅️page = ' + JSON.stringify(page))
      return Base.createSuccessResponse(cb, page)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
