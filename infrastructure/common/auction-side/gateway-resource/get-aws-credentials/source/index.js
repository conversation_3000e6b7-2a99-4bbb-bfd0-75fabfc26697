const {fromTemporaryCredentials} = require('@aws-sdk/credential-providers');
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);

const getUploadRoleByType = type => {
  switch (type) {
    case process.env.MEMBER_REQUEST_UPLOAD:
      return process.env.UPLOAD_MEMBER_REQUEST_FILE_ROLE_ARN;
    case process.env.INQUIRY_REQUEST_UPLOAD:
      return process.env.UPLOAD_INQUIRY_REQUEST_FILE_ROLE_ARN;
    default:
      return '';
  }
};

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const base = new Base(null, params.languageCode);
  const validator = new Validator(base);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return validator.validate(params);
    })
    .then(() => {
      const get = fromTemporaryCredentials({
        params: {
          DurationSeconds: process.env.AWS_CREDENTIALS_DURATION_SECONDS,
          RoleArn: getUploadRoleByType(params.type),
        },
      });
      return get();
    })
    .then(data => {
      const response = {
        bucket: process.env.S3_BUCKET,
        region: process.env.AWS_REGION,
        prefix_key: `${params.type}/${Common.dateToString(new Date())}-${base.randomString(10)}`,
        credentials: data,
      };
      return base.createSuccessResponse(cb, response);
    })
    .catch(error => {
      console.log(error);
      return base.createErrorResponse(cb, error);
    });
};
