const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

exports.handle = function (e, ctx, cb) {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);

  const chatMessage = params.message;
  const chatType = params.chatType;
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      const sqlParams = [
        tenant.tenant_no,
        authorizer.member_no,
        params.exhibition_item_no,
      ];
      return pool.query(
        'SELECT * FROM "f_get_exhibition_message"($1,$2,$3);',
        sqlParams
      );
    })
    .then(data => {
      const result = {
        chatData: data,
        loginFlag: authorizer.member_no ? true : false,
      };
      return base.createSuccessResponse(cb, result);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
