const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);
  let tenant = null;

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base.checkOrigin(header.origin || header.Origin).then(data => {
        tenant = data;
      });
    })
    .then(() => {
      const country = params.registerData?.country;
      console.log(`country = ${country}`);
      if (country === 'JP') {
        const rules = require('./jp-validation-rules');
        return Promise.resolve(rules);
      }
      const rules = require('./other-validation-rules');
      return Promise.resolve(rules);
    })
    .then(rules => {
      console.log('INPUT VALIDATION');
      const registerData = params.registerData;
      const checkData = Object.assign({}, registerData);

      return Promise.resolve()
        .then(() => {
          console.log('Check Duplicated Email');
          console.log('Email: ', checkData.email);
          checkData.emailDuplicated = 0;
          if (checkData?.email) {
            return pool
              .query('SELECT * FROM "f_get_member_by_email"($1,$2);', [
                tenant.tenant_no,
                checkData.email,
              ])
              .then(members => {
                console.log('members: ', members);
                if (members && members.length > 0) {
                  checkData.emailDuplicated = -1;
                }
                console.log(
                  'checkData.emailDuplicated: ',
                  checkData.emailDuplicated
                );
                return Promise.resolve();
              });
          }
          return Promise.resolve();
        })
        .then(() => {
          console.log('FINAL VALIDATION');
          return validator.validate(checkData, rules);
        });
    })
    .then(() => {
      console.log('INSERT MEMBER REQUEST');
      if (params.validateFlag) {
        return Promise.resolve({});
      }

      let tokenConstant = {};
      return Promise.resolve()
        .then(() => {
          // Get token constants
          return pool
            .query('SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);', [
              tenant.tenant_no,
              ['MEMBER_REQUEST_TOKEN_EXPIRED'],
              base.language,
            ])
            .then(data => {
              tokenConstant =
                data.find(
                  content =>
                    content.key_string === 'MEMBER_REQUEST_TOKEN_EXPIRED'
                ) || {};
              return Promise.resolve();
            });
        })
        .then(() => base.hashPassword(params.registerData.password))
        .then(hash => {
          params.registerData.password = hash;
          const {password, ...freeField} = params.registerData;

          // Get token, token expired info
          const now = new Date();
          let tokenExpiredDate = null;
          tokenString = null;
          if (tokenConstant.value1) {
            const expiredPitch = Number(tokenConstant.value1);
            tokenExpiredDate = new Date();
            tokenExpiredDate.setHours(now.getHours() + expiredPitch);
            tokenString = Common.randomNumber(6);
          }

          // return pool.query('SELECT * FROM "f_create_member"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10);', [
          //   tenant.tenant_no,
          //   freeField,
          //   1,
          //   1,
          //   password,
          //   0, // require_password_change
          //   1, // require_confirm_token
          //   tokenString,
          //   tokenExpiredDate,
          //   1 // admin_no
          // ])

          return pool.query(
            'SELECT * FROM "f_insert_member_request"($1,$2,$3);',
            [
              tenant.tenant_no,
              1,
              {
                ...freeField,
                password,
                lang: base.language,
              },
            ]
          );
        })
        .then(() => {
          const member = params.registerData;

          return Promise.all([
            Promise.resolve()
              .then(() => {
                const language = params.registerData.emailLang || 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    [
                      'EMAIL_MEMBER_REQUEST_FOR_MEMBER',
                      'EMAIL_COMMON_FOOTER',
                      'EMAIL_FROM',
                    ],
                    language,
                  ]
                );
              })
              .then(constants => {
                const footer =
                  constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
                  {};
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_MEMBER_REQUEST_FOR_MEMBER'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = [member.email];
                const bcc = mail.value3 ? mail.value3.split(',') : [];
                const content = Common.format(mail.value4, [footer.value4]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
            Promise.resolve()
              .then(() => {
                const language = 'ja';
                return pool.query(
                  'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                  [
                    tenant.tenant_no,
                    ['EMAIL_MEMBER_REQUEST_FOR_ADMIN', 'EMAIL_FROM'],
                    language,
                  ]
                );
              })
              .then(constants => {
                const mailFrom =
                  constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                  null;
                const mail =
                  constants.find(
                    x => x.key_string === 'EMAIL_MEMBER_REQUEST_FOR_ADMIN'
                  ) || {};
                const title = mail.value1;
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2;
                const receivers = mail.value3 ? mail.value3.split(',') : [];
                const bcc = [];

                // Tel
                const tel = member.telCountryCode
                  ? `${member.telCountryCode} ${member.tel}`
                  : member.tel;

                const content = Common.format(mail.value4, [
                  member.companyName || '',
                  member.companyName || '',
                  tel || '',
                  member.email || '',
                ]);
                return Common.sendMailBySES(
                  title,
                  content,
                  sender,
                  receivers,
                  bcc
                );
              }),
          ]);
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
