const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);
  let tenant = null;

  const Rules = require('./validation-rules');

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base
        .checkOrigin(header.origin || header.Origin || e.context?.access_ip)
        .then(data => {
          tenant = data;
        });
    })
    .then(() => {
      console.log('INPUT VALIDATION');
      const checkData = {...params};
      // Passwordを入力する場合のみチェックを行う
      const password = checkData.password;
      checkData.passwordConfirmCpr = 0;
      if (password?.length > 0 || checkData.currentPassword?.length > 0) {
        // // Confirm password
        if (password !== checkData.passwordConfirm) {
          checkData.passwordConfirmCpr = -1;
        }
      }
      return Promise.resolve(checkData);
    })
    .then(checkData => {
      console.log('FINAL VALIDATION');
      return validator
        .validate(checkData, Rules)
        .then(() => {
          console.log('Validation successful');
          return Promise.resolve();
        })
        .catch(
          (
            validateResult = {
              status: 400,
              errors: {},
            }
          ) => {
            console.log('Validation Fail');
            console.log('validateResult: ', validateResult);
            return Promise.reject(validateResult);
          }
        );
    })
    .then(() => {
      return base
        .passwordVerify(
          params.currentPassword,
          authorizer.user_id,
          tenant.tenant_no
        )
        .then(user => {
          return Promise.resolve(user);
        })
        .catch(error => {
          if (error.status === 401) {
            return Promise.reject({
              status: 400,
              errors: {
                currentPassword: base.define.message.E000021,
              },
            });
          }
          return Promise.reject(error);
        });
    })
    .then(user => {
      return Promise.resolve()
        .then(() => {
          if (params.password) {
            return base.hashPassword(params.password);
          }
          return Promise.resolve(null);
        })
        .then(newPassword => {
          return pool.query(
            'SELECT * FROM "f_update_member_password"($1,$2,$3);',
            [tenant.tenant_no, authorizer.member_no, newPassword]
          );
        });
      // .then(() => {
      //   const language = 'ja'
      //   return pool.query(
      //     'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
      //     [
      //       tenant.tenant_no,
      //       [
      //         'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER',
      //         'EMAIL_CHANGE_MEMBER_INFO_FOR_ADMIN',
      //         'EMAIL_COMMON_FOOTER',
      //       ],
      //       language,
      //     ]
      //   )
      // })
      // .then(constants => {
      //   const footer
      //       = constants.find(constant => constant.key_string === 'EMAIL_COMMON_FOOTER') || {}
      //   return Promise.all([
      //     Promise.resolve().then(() => {
      //       const mail
      //           = constants.find(constant => constant.key_string
      //               === 'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER') || {}
      //       const title = mail.value1
      //       const sender = mail.value2
      //       const receivers = [user.free_field.email]
      //       const bcc = mail.value3 ? mail.value3.split(',') : []
      //       const content = Common.format(mail.value4, [
      //         authorizer.user_name || '', // 0
      //         footer.value4
      //       ])

      //       return Common.sendMailBySES(
      //         title,
      //         content,
      //         sender,
      //         receivers,
      //         bcc
      //       )
      //     }),
      //     Promise.resolve().then(() => {
      //       const mail
      //           = constants.find(constant => constant.key_string
      //               === 'EMAIL_CHANGE_MEMBER_INFO_FOR_ADMIN') || {}
      //       const title = mail.value1
      //       const sender = mail.value2
      //       const receivers = mail.value3 ? mail.value3.split(',') : []
      //       const bcc = []
      //       const content = Common.format(mail.value4, [authorizer.user_name || ''])
      //       return Common.sendMailBySES(
      //         title,
      //         content,
      //         sender,
      //         receivers,
      //         bcc
      //       )
      //     }),
      //   ])
      // })
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
