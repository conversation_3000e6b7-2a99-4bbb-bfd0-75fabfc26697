const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)

exports.handle = (e, ctx, cb) => {
  console.log('📚 log of event : ', e)
  console.log('📚 log of context : ', ctx)
  const params = Common.parseRequestBody(e.body)
  console.log('📌 log of refresh-item params : ', params)
  const header = e.headers
  const base = new Base(pool, params.languageCode)
  const validator = new Validator(base)

  let tenant = null

  const memberInfo = Common.extractMemberInfoFromToken(header.Authorization || header.authorization)
  console.log('🎯 Extracted member info from token:', memberInfo)

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base.checkOrigin(header.origin || header.Origin).then(data => {
        console.log('🏢 Tenant info:', data)
        tenant = data
        return Promise.resolve()
      })
    })
    .then(() => validator.validate(params))
    .then(() => {
      const authorizer = {
        member_no: memberInfo ? memberInfo.member_no : null,
        tenant_no: tenant.tenant_no,
      }
      console.log('🏞 log of authorizer : ', authorizer)

      const sqlParams = [
        params.manageNo,
        tenant.tenant_no,
        base.language,
        authorizer.member_no,
      ]
      console.log('🔎 SQL params:', JSON.stringify(sqlParams, null, 2))
      console.log('🎯 Member info used in query:', memberInfo ? 'Available' : 'Not available')
      const sql = `SELECT * FROM "f_refresh_item_and_count_up_view"(${Common.sqlParamNumber(sqlParams.length)});`
      console.log('🗂 SQL query:', sql)

      return pool.rlsQuery(authorizer.tenant_no, sql, sqlParams)
    })
    .then(result => {
      console.log('☎️ log of query f_refresh_item_and_count_up_view : ', result)
      const data = result?.length > 0 ? result[0] : null
      if (result?.length > 0) {
        return base.createSuccessResponse(cb, data)
      }
      const response = {
        status: 400,
        errors: {
          manageNo: base.define.message.E100249,
        },
      }
      return Promise.reject(response)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
