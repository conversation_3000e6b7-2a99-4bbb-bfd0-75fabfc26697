const {GetObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);
  const inquiryData = Object.assign({}, params, params.inquiryData);

  const rules = require('./validation-rules');

  // Get language
  let lang = base.language;
  let memberTel = '';

  const s3 = new S3Client({});
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      // Check email confirmation
      if (
        inquiryData.emailConfirm &&
        inquiryData.emailConfirm !== inquiryData.email
      ) {
        rules.emailConfirm.PATTERN_CHECK = true;
        inquiryData.emailConfirm = -1;
      } else {
        rules.emailConfirm.PATTERN_CHECK = false;
      }
      return validator.validate(inquiryData, rules);
    })
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      if (!authorizer.member_no) {
        return Promise.resolve(tenant);
      }
      // Get language
      return pool
        .query('SELECT * FROM "f_get_member_info_by_member_no"($1,$2);', [
          authorizer.member_no,
          tenant.tenant_no,
        ])
        .then(members => {
          if (members && members.length > 0) {
            const member = members[0];
            lang =
              member.free_field.emailLang ||
              member.free_field.lang ||
              lang ||
              'en';

            // Tel
            memberTel = member.free_field.telCountryCode
              ? `${member.free_field.telCountryCode} ${member.free_field.tel}`
              : member.free_field.tel;
          }
          return Promise.resolve(tenant);
        });
    })
    .then(tenant => {
      if (params.validateFlag) {
        return Promise.resolve({});
      }
      return Promise.resolve()
        .then(() => {
          // exhibitionItemNoがある場合、クエリ実行する
          if (params.exhibitionItemNo) {
            return pool
              .query('SELECT * FROM "f_get_item_info"($1,$2,$3,$4);', [
                params.exhibitionItemNo,
                tenant.tenant_no,
                lang,
                9999,
              ])
              .then(result => {
                return Promise.resolve(
                  result.length > 0
                    ? {
                        manageNo: result[0].manage_no,
                        itemNo: result[0].exhibition_item_no,
                        productName: result[0].free_field.product_name,
                        maker: result[0].free_field.maker,
                        capacity: result[0].free_field.capacity,
                        color: result[0].free_field.color,
                        sim: result[0].free_field.sim,
                        rank: result[0].free_field.rank,
                        note1: result[0].free_field.note1,
                        note2: result[0].free_field.note2,
                        exhibitionFlag: true,
                      }
                    : {}
                );
              });
          }
          return Promise.resolve({});
        })
        .then(result => {
          const data = Object.assign({}, result, params.inquiryData);
          return pool
            .query(
              'SELECT * FROM "f_insert_inquiry_request"($1,$2,$3,$4,$5,$6,$7);',
              [
                tenant.tenant_no,
                params.exhibitionItemNo ? 1 : 9, // 1：商品へのお問い合わせ 9：各種お問い合わせ
                data,
                params.content,
                params.files,
                1,
                authorizer.member_no,
              ]
            )
            .then(() => {
              return Promise.all([
                Promise.resolve()
                  .then(() => {
                    return pool.query(
                      'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                      [
                        tenant.tenant_no,
                        [
                          'EMAIL_INQUIRY_REQUEST_FOR_ADMIN',
                          'EMAIL_INQUIRY_REQUEST_FOR_MEMBER',
                          'EMAIL_COMMON_FOOTER',
                          'EMAIL_FROM',
                        ],
                        lang,
                      ]
                    );
                  })
                  .then(constants => {
                    const mailFrom =
                      constants.find(x => x.key_string === 'EMAIL_FROM')
                        ?.value2 || null;
                    const footer =
                      constants.find(
                        x => x.key_string === 'EMAIL_COMMON_FOOTER'
                      ) || {};
                    const mail =
                      constants.find(
                        x => x.key_string === 'EMAIL_INQUIRY_REQUEST_FOR_MEMBER'
                      ) || {};
                    const title = mail.value1;
                    const sender = mailFrom
                      ? `"${mailFrom}"<${mail.value2}>`
                      : mail.value2;
                    const receivers = [params.inquiryData.email];
                    const bcc = mail.value3 ? mail.value3.split(',') : [];

                    const fields = mail.value5.split(',');
                    const content = params.exhibitionItemNo
                      ? Common.format(mail.value4, [
                          [
                            '',
                            fields[0],
                            `${fields[1]}: ${data.productName}`,
                            '',
                          ].join('\n'),
                          params.content || '',
                          footer.value4,
                        ])
                      : Common.format(mail.value4, [
                          '\n',
                          params.content || '',
                          footer.value4,
                        ]);

                    return Common.sendMailBySES(
                      title,
                      content,
                      sender,
                      receivers,
                      bcc
                    );
                  }),
                Promise.resolve()
                  .then(() => {
                    return pool.query(
                      'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                      [
                        tenant.tenant_no,
                        ['EMAIL_INQUIRY_REQUEST_FOR_ADMIN', 'EMAIL_FROM'],
                        'ja',
                      ]
                    );
                  })
                  .then(constants => {
                    const mailFrom =
                      constants.find(x => x.key_string === 'EMAIL_FROM')
                        ?.value2 || null;
                    const mail =
                      constants.find(
                        constant =>
                          constant.key_string ===
                          'EMAIL_INQUIRY_REQUEST_FOR_ADMIN'
                      ) || {};
                    const title = mail.value1;
                    const sender = mailFrom
                      ? `"${mailFrom}"<${mail.value2}>`
                      : mail.value2;
                    const receivers = mail.value3 ? mail.value3.split(',') : [];
                    const bcc = [];

                    const fields = mail.value5.split(',');
                    const content = params.exhibitionItemNo
                      ? Common.format(mail.value4, [
                          authorizer.member_id ? data.companyName : 'ゲスト',
                          authorizer.member_id || '',
                          data.companyName || '',
                          data.tel || memberTel || '',
                          data.email || '',
                          [
                            '',
                            fields[0],
                            `${fields[1]}: ${data.productName}`,
                            '',
                          ].join('\n'),
                          params.content || '',
                        ])
                      : Common.format(mail.value4, [
                          authorizer.member_id ? data.companyName : 'ゲスト',
                          authorizer.member_id || '',
                          data.companyName || '',
                          data.tel || memberTel || '',
                          data.email || '',
                          '\n',
                          params.content || '',
                        ]);
                    return Common.sendRawMailBySES(
                      title,
                      content,
                      sender,
                      receivers,
                      bcc,
                      [] // files not used
                    );
                  }),
              ]);
            });
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
